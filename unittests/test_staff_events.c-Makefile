# Makefile for test_staff_events.c  
# LuminariMUD staff event system unit tests

CC = gcc
CFLAGS = -g -Wall -I../src -I../.. -DTEST_BUILD
LDFLAGS = 
LIBS = 

# Source files
TEST_SRCS = test_staff_events.c CuTest/CuTest.c
STAFF_EVENT_SRCS = ../src/staff_events.c
MOCK_SRCS = mocks.c

# Required function implementations
IMPL_SRCS = ../src/utils.c ../src/random.c ../src/db.c ../src/comm.c

# Object files
TEST_OBJS = $(TEST_SRCS:.c=.o)
STAFF_EVENT_OBJS = $(STAFF_EVENT_SRCS:.c=.o)
MOCK_OBJS = $(MOCK_SRCS:.c=.o)
IMPL_OBJS = $(IMPL_SRCS:.c=.o)

# Target executable
TARGET = test_staff_events

# Default target
all: $(TARGET)

# Link the test executable
$(TARGET): $(TEST_OBJS) staff_events_test.o
	$(CC) $(LDFLAGS) -o $@ $^ $(LIBS)

# Special rule for staff_events.c to avoid main() conflict
staff_events_test.o: ../src/staff_events.c
	$(CC) $(CFLAGS) -DTEST_BUILD -c -o $@ $<

# Compile test files
%.o: %.c
	$(CC) $(CFLAGS) -c -o $@ $<

# Clean up
clean:
	rm -f $(TEST_OBJS) staff_events_test.o $(TARGET)

# Run the tests
test: $(TARGET)
	./$(TARGET)

.PHONY: all clean test