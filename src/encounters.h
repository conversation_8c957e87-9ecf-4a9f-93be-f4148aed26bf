
#define ENCOUNTER_CLASS_NONE               0
#define ENCOUNTER_CLASS_COMBAT             1

#define NUM_ENCOUNTER_CLASSES              2

#define ENCOUNTER_TYPE_NONE                 0
#define ENCOUNTER_TYPE_GOBLIN_1             1
#define ENCOUNTER_TYPE_GOBLIN_2             2
#define ENCOUNTER_TYPE_KING_CRAB_1          3
#define ENCOUNTER_TYPE_KOBOLD_1             4
#define ENCOUNTER_TYPE_GIANT_RAT_1          5
#define ENCOUNTER_TYPE_SEWER_CENTIPEDE      6
#define ENCOUNTER_TYPE_SEA_WASP_JELLYFISH   7
#define ENCOUNTER_TYPE_MANDRILL             8
#define ENCOUNTER_TYPE_MUCKDWELLER          9
#define ENCOUNTER_TYPE_BARRACUDA            10
#define ENCOUNTER_TYPE_FIRE_BEETLE          11
#define ENCOUNTER_TYPE_DROW_WARRIOR         12
#define ENCOUNTER_TYPE_DROW_PRIESTESS       13
#define ENCOUNTER_TYPE_DROW_WIZARD          14
#define ENCOUNTER_TYPE_DUERGAR_WARRIOR      15
#define ENCOUNTER_TYPE_HAWK                 16
#define ENCOUNTER_TYPE_MERFOLK_HUNTER       17
#define ENCOUNTER_TYPE_ORCISH_WARRIOR       18
#define ENCOUNTER_TYPE_OWL                  19
#define ENCOUNTER_TYPE_RATFOLK_WANDERER     20
#define ENCOUNTER_TYPE_SEAL                 21
#define ENCOUNTER_TYPE_WANDERING_SKELETON   22
#define ENCOUNTER_TYPE_SPRITE               23
#define ENCOUNTER_TYPE_GIANT_MOSQUITO       24
#define ENCOUNTER_TYPE_ANTELOPE             25
#define ENCOUNTER_TYPE_BABOON               26
#define ENCOUNTER_TYPE_BADGER               27
#define ENCOUNTER_TYPE_DOLPHIN              28
#define ENCOUNTER_TYPE_EAGLE                29
#define ENCOUNTER_TYPE_HOBGOBLIN_SOLDIER    30
#define ENCOUNTER_TYPE_HOBGOBLIN_SERGEANT   31
#define ENCOUNTER_TYPE_BLUE_RINGED_OCTOPUS  32
#define ENCOUNTER_TYPE_SAGARI               33
#define ENCOUNTER_TYPE_VIPER                34
#define ENCOUNTER_TYPE_GIANT_CRAB_SPIDER    35
#define ENCOUNTER_TYPE_STINGRAY             36
#define ENCOUNTER_TYPE_STIRGE               37
#define ENCOUNTER_TYPE_SYLPH                38
#define ENCOUNTER_TYPE_TENGU                39
#define ENCOUNTER_TYPE_SNAPPING_TURTLE      40
#define ENCOUNTER_TYPE_UNDINE               41
#define ENCOUNTER_TYPE_VEGEPYGMY            42
#define ENCOUNTER_TYPE_VULTURE              43
#define ENCOUNTER_TYPE_WANDERING_ZOMBIE     44
#define ENCOUNTER_TYPE_GREMLIN              45
#define ENCOUNTER_TYPE_MANTARI              46
#define ENCOUNTER_TYPE_GIANT_AMOEBA         47
#define ENCOUNTER_TYPE_GIANT_BEE            48
#define ENCOUNTER_TYPE_BROWNIE              49
#define ENCOUNTER_TYPE_CAMEL                50
#define ENCOUNTER_TYPE_CLAWBAT              51
#define ENCOUNTER_TYPE_DARKMANTLE           52
#define ENCOUNTER_TYPE_DIRE_CORBY           53
#define ENCOUNTER_TYPE_ELK                  54
#define ENCOUNTER_TYPE_GIANT_FROG           55
#define ENCOUNTER_TYPE_GHOUL                56
#define ENCOUNTER_TYPE_GNOLL                57
#define ENCOUNTER_TYPE_HIPPOCAMPUS          58
#define ENCOUNTER_TYPE_HYENA                59
#define ENCOUNTER_TYPE_LIZARDFOLK           60
#define ENCOUNTER_TYPE_GIANT_GECKO_LIZARD   61
#define ENCOUNTER_TYPE_MONGRELMAN           62
#define ENCOUNTER_TYPE_NINGYO               63
#define ENCOUNTER_TYPE_NIXIE                64
#define ENCOUNTER_TYPE_PSEUDODRAGON         65
#define ENCOUNTER_TYPE_RAM                  66
#define ENCOUNTER_TYPE_MANTA_RAY            67
#define ENCOUNTER_TYPE_REEFCLAW             68
#define ENCOUNTER_TYPE_GIANT_SPIDER         69
#define ENCOUNTER_TYPE_SQUID                70
#define ENCOUNTER_TYPE_STRIX                71
#define ENCOUNTER_TYPE_TROGLODYTE           72
#define ENCOUNTER_TYPE_WOLF                 73
#define ENCOUNTER_TYPE_HALF_OGRE            74
#define ENCOUNTER_TYPE_MANDRAGORA           75
#define ENCOUNTER_TYPE_OGRILLON             76
#define ENCOUNTER_TYPE_OROG                 77
#define ENCOUNTER_TYPE_GIANT_ANT            78
#define ENCOUNTER_TYPE_AXE_BEAK             79
#define ENCOUNTER_TYPE_DIRE_BADGER          80
#define ENCOUNTER_TYPE_DIRE_BAT             81
#define ENCOUNTER_TYPE_BLINK_DOG            82
#define ENCOUNTER_TYPE_BOAR                 83
#define ENCOUNTER_TYPE_BOGGARD              84
#define ENCOUNTER_TYPE_BOG_STRIDER          85
#define ENCOUNTER_TYPE_BUGBEAR              86
#define ENCOUNTER_TYPE_CROCODILE            87
#define ENCOUNTER_TYPE_FAERIE_DRAGON        88
#define ENCOUNTER_TYPE_DRAUGR               89
#define ENCOUNTER_TYPE_GORILLA              90
#define ENCOUNTER_TYPE_HIPPOGRIFF           91
#define ENCOUNTER_TYPE_ALLIP                92
#define ENCOUNTER_TYPE_ANKHEG               93
#define ENCOUNTER_TYPE_CENTAUR              94
#define ENCOUNTER_TYPE_OGRE                 95
#define ENCOUNTER_TYPE_ETTIN                96
#define ENCOUNTER_TYPE_LAMIA                97
#define ENCOUNTER_TYPE_MOTHMAN              98
#define ENCOUNTER_TYPE_REVENANT             99
#define ENCOUNTER_TYPE_SHAMBLING_MOUND      100
#define ENCOUNTER_TYPE_TROLL                101
#define ENCOUNTER_TYPE_WYVERN               102
#define ENCOUNTER_TYPE_COUATL               103
#define ENCOUNTER_TYPE_GIANT_FLYTRAP        104
#define ENCOUNTER_TYPE_RAKSHASA             105
#define ENCOUNTER_TYPE_STONE_GIANT          106
#define ENCOUNTER_TYPE_GORGON               107
#define ENCOUNTER_TYPE_OGRE_MAGE            108
#define ENCOUNTER_TYPE_HYDRA                109
#define ENCOUNTER_TYPE_STONE_GOLEM          110
#define ENCOUNTER_TYPE_NAGA                 111
#define ENCOUNTER_TYPE_VAMPIRE              112
#define ENCOUNTER_TYPE_CYCLOPS              113
#define ENCOUNTER_TYPE_GRIZZLY_BEAR         114
#define ENCOUNTER_TYPE_GIANT_STAG_BEETLE    115
#define ENCOUNTER_TYPE_BISON                116
#define ENCOUNTER_TYPE_DIRE_BOAR            117
#define ENCOUNTER_TYPE_BOTFLY_SWARM         118
#define ENCOUNTER_TYPE_GIANT_DRAGONFLY      119
#define ENCOUNTER_TYPE_FOREST_DRAKE         120
#define ENCOUNTER_TYPE_DUST_DIGGER          121
#define ENCOUNTER_TYPE_HILL_GIANT           122
#define ENCOUNTER_TYPE_FIRE_GIANT           123

#define NUM_ENCOUNTER_TYPES                 124

#define ENCOUNTER_GROUP_TYPE_NONE           0
#define ENCOUNTER_GROUP_TYPE_GOBLINS        1
#define ENCOUNTER_GROUP_TYPE_KING_CRABS     2
#define ENCOUNTER_GROUP_TYPE_KOBOLDS        3
#define ENCOUNTER_GROUP_TYPE_GIANT_RATS     4
#define ENCOUNTER_GROUP_TYPE_CENTIPEDES     5
#define ENCOUNTER_GROUP_TYPE_SEA_WASP_JELLYFISH 6
#define ENCOUNTER_GROUP_TYPE_MANDRILL       7
#define ENCOUNTER_GROUP_TYPE_MUCKDWELLER    8
#define ENCOUNTER_GROUP_TYPE_BARRACUDA      9
#define ENCOUNTER_GROUP_TYPE_FIRE_BEETLE    10
#define ENCOUNTER_GROUP_TYPE_DROW_PATROL    11
#define ENCOUNTER_GROUP_TYPE_DUERGAR_PATROL 12
#define ENCOUNTER_GROUP_TYPE_HAWK           13
#define ENCOUNTER_GROUP_TYPE_MERFOLK        14
#define ENCOUNTER_GROUP_TYPE_ORC_PATROL     15
#define ENCOUNTER_GROUP_TYPE_OWL            16
#define ENCOUNTER_GROUP_TYPE_RATFOLK        17
#define ENCOUNTER_GROUP_TYPE_SEAL           18
#define ENCOUNTER_GROUP_TYPE_WANDERING_SKELETON 19
#define ENCOUNTER_GROUP_TYPE_SPRITE         20
#define ENCOUNTER_GROUP_TYPE_GIANT_MOSQUITO 21
#define ENCOUNTER_GROUP_TYPE_ANTELOPE       22
#define ENCOUNTER_GROUP_TYPE_BABOON         23
#define ENCOUNTER_GROUP_TYPE_BADGER         24
#define ENCOUNTER_GROUP_TYPE_DOLPHIN        25
#define ENCOUNTER_GROUP_TYPE_EAGLE          26
#define ENCOUNTER_GROUP_TYPE_HOBGOBLINS     27
#define ENCOUNTER_GROUP_TYPE_BLUE_RINGED_OCTOPUS 28
#define ENCOUNTER_GROUP_TYPE_SAGARI         29
#define ENCOUNTER_GROUP_TYPE_VIPER          30
#define ENCOUNTER_GROUP_TYPE_GIANT_CRAB_SPIDER 31
#define ENCOUNTER_GROUP_TYPE_STINGRAY       32
#define ENCOUNTER_GROUP_TYPE_STIRGE         33
#define ENCOUNTER_GROUP_TYPE_SYLPH          34
#define ENCOUNTER_GROUP_TYPE_TENGU          35
#define ENCOUNTER_GROUP_TYPE_SNAPPING_TURTLE 36
#define ENCOUNTER_GROUP_TYPE_UNDINE         37
#define ENCOUNTER_GROUP_TYPE_VEGEPYGMY      38
#define ENCOUNTER_GROUP_TYPE_VULTURE        39
#define ENCOUNTER_GROUP_TYPE_WANDERING_ZOMBIE 40
#define ENCOUNTER_GROUP_TYPE_GREMLIN        41
#define ENCOUNTER_GROUP_TYPE_MANTARI        42
#define ENCOUNTER_GROUP_TYPE_GIANT_AMOEBA   43
#define ENCOUNTER_GROUP_TYPE_GIANT_BEE      44
#define ENCOUNTER_GROUP_TYPE_BROWNIE        45
#define ENCOUNTER_GROUP_TYPE_CAMEL          46
#define ENCOUNTER_GROUP_TYPE_CLAWBAT        47
#define ENCOUNTER_GROUP_TYPE_DARKMANTLE     48
#define ENCOUNTER_GROUP_TYPE_DIRE_CORBY     49
#define ENCOUNTER_GROUP_TYPE_ELK            50
#define ENCOUNTER_GROUP_TYPE_GIANT_FROG     51
#define ENCOUNTER_GROUP_TYPE_GHOUL          52
#define ENCOUNTER_GROUP_TYPE_GNOLL          53
#define ENCOUNTER_GROUP_TYPE_HIPPOCAMPUS    54
#define ENCOUNTER_GROUP_TYPE_HYENA          55
#define ENCOUNTER_GROUP_TYPE_LIZARDFOLK     56
#define ENCOUNTER_GROUP_TYPE_GIANT_GECKO_LIZARD 57
#define ENCOUNTER_GROUP_TYPE_MONGRELMAN     58
#define ENCOUNTER_GROUP_TYPE_NINGYO         59
#define ENCOUNTER_GROUP_TYPE_NIXIE          60
#define ENCOUNTER_GROUP_TYPE_PSEUDODRAGON   61
#define ENCOUNTER_GROUP_TYPE_RAM            62
#define ENCOUNTER_GROUP_TYPE_MANTA_RAY      63
#define ENCOUNTER_GROUP_TYPE_REEFCLAW       64
#define ENCOUNTER_GROUP_TYPE_GIANT_SPIDER   65
#define ENCOUNTER_GROUP_TYPE_SQUID          66
#define ENCOUNTER_GROUP_TYPE_STRIX          67
#define ENCOUNTER_GROUP_TYPE_TROGLODYTE     68
#define ENCOUNTER_GROUP_TYPE_WOLF           69
#define ENCOUNTER_GROUP_TYPE_HALF_OGRE      70
#define ENCOUNTER_GROUP_TYPE_MANDRAGORA     71
#define ENCOUNTER_GROUP_TYPE_OGRILLON       72
#define ENCOUNTER_GROUP_TYPE_OROG           73
#define ENCOUNTER_GROUP_TYPE_GIANT_ANT      74
#define ENCOUNTER_GROUP_TYPE_AXE_BEAK       75
#define ENCOUNTER_GROUP_TYPE_DIRE_BADGER    76
#define ENCOUNTER_GROUP_TYPE_DIRE_BAT       77
#define ENCOUNTER_GROUP_TYPE_BLINK_DOG      78
#define ENCOUNTER_GROUP_TYPE_BOAR           79
#define ENCOUNTER_GROUP_TYPE_BOGGARD        80
#define ENCOUNTER_GROUP_TYPE_BOG_STRIDER    81
#define ENCOUNTER_GROUP_TYPE_BUGBEAR        82
#define ENCOUNTER_GROUP_TYPE_CROCODILE      83
#define ENCOUNTER_GROUP_TYPE_FAERIE_DRAGON  84
#define ENCOUNTER_GROUP_TYPE_DRAUGR         85
#define ENCOUNTER_GROUP_TYPE_GORILLA        86
#define ENCOUNTER_GROUP_TYPE_HIPPOGRIFF     87
#define ENCOUNTER_GROUP_TYPE_ALLIP          88
#define ENCOUNTER_GROUP_TYPE_ANKHEG         89
#define ENCOUNTER_GROUP_TYPE_CENTAUR        90
#define ENCOUNTER_GROUP_TYPE_OGRE           91
#define ENCOUNTER_GROUP_TYPE_ETTIN          92
#define ENCOUNTER_GROUP_TYPE_LAMIA          93
#define ENCOUNTER_GROUP_TYPE_MOTHMAN        94
#define ENCOUNTER_GROUP_TYPE_REVENANT       95
#define ENCOUNTER_GROUP_TYPE_SHAMBLING_MOUND 96
#define ENCOUNTER_GROUP_TYPE_TROLL          97
#define ENCOUNTER_GROUP_TYPE_WYVERN         98
#define ENCOUNTER_GROUP_TYPE_COUATL         99
#define ENCOUNTER_GROUP_TYPE_GIANT_FLYTRAP  100
#define ENCOUNTER_GROUP_TYPE_RAKSHASA       101
#define ENCOUNTER_GROUP_TYPE_STONE_GIANT    102
#define ENCOUNTER_GROUP_TYPE_GORGON         103
#define ENCOUNTER_GROUP_TYPE_OGRE_MAGE      104
#define ENCOUNTER_GROUP_TYPE_HYDRA          105
#define ENCOUNTER_GROUP_TYPE_STONE_GOLEM    106
#define ENCOUNTER_GROUP_TYPE_NAGA           107
#define ENCOUNTER_GROUP_TYPE_VAMPIRE        108
#define ENCOUNTER_GROUP_TYPE_CYCLOPS        109
#define ENCOUNTER_GROUP_TYPE_GRIZZLY_BEAR   110
#define ENCOUNTER_GROUP_TYPE_GIANT_STAG_BEETLE 111
#define ENCOUNTER_GROUP_TYPE_BISON          112
#define ENCOUNTER_GROUP_TYPE_DIRE_BOAR      113
#define ENCOUNTER_GROUP_TYPE_BOTFLY_SWARM   114
#define ENCOUNTER_GROUP_TYPE_GIANT_DRAGONFLY 115
#define ENCOUNTER_GROUP_TYPE_FOREST_DRAKE   116
#define ENCOUNTER_GROUP_TYPE_DUST_DIGGER    117
#define ENCOUNTER_GROUP_TYPE_HILL_GIANT     118
#define ENCOUNTER_GROUP_TYPE_FIRE_GIANT     119

#define NUM_ENCOUNTER_GROUP_TYPES           120

#define ENCOUNTER_STRENGTH_NORMAL          0
#define ENCOUNTER_STRENGTH_BOSS            1

#define NUM_ENCOUNTER_STRENGTHS            2

#define TREASURE_TABLE_NONE                0
#define TREASURE_TABLE_LOW_NORM            1
#define TREASURE_TABLE_LOW_BOSS            2
#define TREASURE_TABLE_LOW_MID_NORM        3
#define TREASURE_TABLE_LOW_MID_BOSS        4
#define TREASURE_TABLE_MID_NORM            5
#define TREASURE_TABLE_MID_BOSS            6
#define TREASURE_TABLE_MID_HIGH_NORM       7
#define TREASURE_TABLE_MID_HIGH_BOSS       8
#define TREASURE_TABLE_HIGH_NORM           9
#define TREASURE_TABLE_HIGH_BOSS           10
#define TREASURE_TABLE_EPIC_LOW_NORM       11
#define TREASURE_TABLE_EPIC_LOW_BOSS       12
#define TREASURE_TABLE_EPIC_MID_NORM       13
#define TREASURE_TABLE_EPIC_MID_BOSS       14
#define TREASURE_TABLE_EPIC_HIGH_NORM      15
#define TREASURE_TABLE_EPIC_HIGH_BOSS      16

#define NUM_TREASURE_TABLES                17

#if defined(CAMPAIGN_DL)
#define ENCOUNTER_MOB_VNUM                 60002
#else
#define ENCOUNTER_MOB_VNUM                 8100
#endif

#define NON_HOSTILE                        false
#define HOSTILE                            true

#define NON_SENTIENT                        false
#define SENTIENT                            true

#define ENCOUNTER_SCMD_DISTRACT             0
#define ENCOUNTER_SCMD_INTIMIDATE           1
#define ENCOUNTER_SCMD_DIPLOMACY            2
#define ENCOUNTER_SCMD_BLUFF                3
#define ENCOUNTER_SCMD_BRIBE                4

struct encounter_data {

  int encounter_type;
  int min_level;
  int max_level;
  int sector_types[NUM_ROOM_SECTORS];
  int encounter_group;
  const char * object_name;
  const char * description;
  const char * long_description;
  int load_chance;
  int min_number;
  int max_number;
  int treasure_table;
  int char_class;
  int encounter_strength;
  int alignment;
  int race_type;
  int subrace[3];
  int size;
  bool hostile;
  bool sentient;
};

extern struct encounter_data encounter_table[NUM_ENCOUNTER_TYPES];

int get_exploration_dc(struct char_data *ch);
bool in_encounter_room(struct char_data *ch);
void check_random_encounter(struct char_data *ch);
int encounter_chance(struct char_data *ch);
void populate_encounter_table(void);
void reset_expire_cooldown(room_rnum rnum);
void set_expire_cooldown(room_rnum rnum);
int encounter_bribe_amount(struct char_data *ch);
bool is_hostile_encounter(struct char_data *ch);
void set_encounter_peaceful(struct char_data *ch);
bool is_peaceful_encounter(struct char_data *ch);
int get_party_slowest_speed(struct char_data *ch);
int get_encounter_mobs_speed(struct char_data *ch);
bool can_encounter_mobs_see_party(struct char_data *ch);
bool encounter_mobs_can_move(struct char_data *ch);
bool encounter_coerce_attempted(struct char_data *ch, int attempt_type);
void set_coersion_attempted(struct char_data *ch, int attempt_type);
void give_gold_to_encounter_mob(struct char_data *ch, int amount);
void set_encounter_to_peaceful(struct char_data *ch);
bool can_coerce_encounter(struct char_data *ch, int attempt_type);
void add_encounter_record(int encounter_record, int encounter_type, int min_level, int max_level, int encounter_group, const char *object_name, 
    int load_chance, int min_number, int max_number, int treasure_table, int char_class, int encounter_strength,
    int alignment, int race_type, int subrace1, int subrace2, int subrace3, bool hostile, bool sentient, int size);
