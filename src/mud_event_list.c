/**************************************************************************
 *  File: mud_event_list.c                             Part of LuminariMUD *
 *  Usage: Event registry and definitions                                  *
 *                                                                         *
 *  This file contains the mud_event_index array which maps event IDs     *
 *  to their handler functions, event types, messages, and feat data.     *
 **************************************************************************/

#include "conf.h"
#include "sysdep.h"
#include "structs.h"
#include "mud_event.h"
#include "dg_event.h"

/* External event handler function declarations */
extern EVENTFUNC(event_countdown);
extern EVENTFUNC(event_daily_use_cooldown);
extern EVENTFUNC(get_protocols);
extern EVENTFUNC(event_whirlwind);
extern EVENTFUNC(event_casting);
extern EVENTFUNC(event_preparation);
extern EVENTFUNC(event_crafting);
extern EVENTFUNC(event_acid_arrow);
extern EVENTFUNC(event_implode);
extern EVENTFUNC(event_ice_storm);
extern EVENTFUNC(event_chain_lightning);
extern EVENTFUNC(event_falling);
extern EVENTFUNC(event_check_occupied);
extern EVENTFUNC(event_tracks);
extern EVENTFUNC(event_combat_round);
extern EVENTFUNC(event_action_cooldown);
extern EVENTFUNC(event_trap_triggered);
extern EVENTFUNC(event_bardic_performance);
extern EVENTFUNC(event_craft);
extern EVENTFUNC(event_copyover);
extern EVENTFUNC(event_spiritual_weapon);
extern EVENTFUNC(event_dancing_weapon);
extern EVENTFUNC(event_holy_javelin);
extern EVENTFUNC(event_moonbeam);
extern EVENTFUNC(event_aqueous_orb);
extern EVENTFUNC(event_concussive_onslaught);
extern EVENTFUNC(event_power_leech);

/* The mud_event_index[] with extended data for table-driven handling
 * Format: {name, func, type, completion_msg, recovery_msg, feat, daily_uses} */
struct mud_event_list mud_event_index[] = {
    /*0*/
    {"Null", NULL, -1, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Protocol", get_protocols, EVENT_DESC, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Whirlwind", event_whirlwind, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Casting", event_casting, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Lay on hands", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to lay on hands again.",
     "One of your lay on hands uses has recovered.",
     FEAT_LAYHANDS, 0},
    /*5*/
    {"Treat injury", event_countdown, EVENT_CHAR,
     "You are now able to treat injuries again.", NULL, FEAT_UNDEFINED, 0},
    {"Taunt Cool Down", event_countdown, EVENT_CHAR,
     "You are now able to taunt again.", NULL, FEAT_UNDEFINED, 0},
    {"Taunted", event_countdown, EVENT_CHAR,
     "You feel the effects of the taunt wear off.", NULL, FEAT_UNDEFINED, 0},
    {"Mummy dust", event_countdown, EVENT_CHAR,
     "You are now able to cast Mummy Dust again.", NULL, FEAT_UNDEFINED, 0},
    {"Dragon knight", event_countdown, EVENT_CHAR,
     "You are now able to cast Dragon Knight again.", NULL, FEAT_UNDEFINED, 0},
    /*10*/
    {"Greater ruin", event_countdown, EVENT_CHAR,
     "You are now able to cast Greater Ruin again.", NULL, FEAT_UNDEFINED, 0},
    {"Hellball", event_countdown, EVENT_CHAR,
     "You are now able to cast Hellball again.", NULL, FEAT_UNDEFINED, 0},
    {"Epic mage armor", event_countdown, EVENT_CHAR,
     "You are now able to cast Epic Mage Armor again.", NULL, FEAT_UNDEFINED, 0},
    {"Epic warding", event_countdown, EVENT_CHAR,
     "You are now able to cast Epic Warding again.", NULL, FEAT_UNDEFINED, 0},
    {"Preparing Spells", event_preparation, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    /*15*/
    {"Stunned", event_countdown, EVENT_CHAR,
     "You are now free from the stunning affect.", NULL, FEAT_UNDEFINED, 0},
    {"Stunning fist", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to strike with your stunning fist again.",
     "One of your stunning fist uses has recovered.",
     FEAT_STUNNING_FIST, 0},
    {"Crafting", event_crafting, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Crystal fist", event_daily_use_cooldown, EVENT_CHAR,
     "You may enhance your unarmed attacks again.",
     "One of your crystal fist uses has recovered.",
     FEAT_CRYSTAL_FIST, 0},
    {"Crystal body", event_daily_use_cooldown, EVENT_CHAR,
     "You may harden your crystalline body again.",
     "One of your crystal body uses has recovered.",
     FEAT_CRYSTAL_BODY, 0},
    /*20*/
    {"Rage", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to Rage again.",
     "One of your rage uses has recovered.",
     FEAT_RAGE, 0},
    {"Acid arrow", event_acid_arrow, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Defensive Roll", event_countdown, EVENT_CHAR,
     "You are now able to 'defensive roll' again.", NULL, FEAT_UNDEFINED, 0},
    {"Purify", event_countdown, EVENT_CHAR,
     "You are now able to 'purify' again.",
     "One of your remove disease (purify) uses has recovered.",
     FEAT_REMOVE_DISEASE, 0},
    {"Call Animal Companion", event_countdown, EVENT_CHAR,
     "You are now able to 'call companion' again.", NULL, FEAT_UNDEFINED, 0},
    {"Call Familiar", event_countdown, EVENT_CHAR,
     "You are now able to 'call familiar' again.", NULL, FEAT_UNDEFINED, 0},
    {"Call Mount", event_countdown, EVENT_CHAR,
     "You are now able to 'call mount' again.", NULL, FEAT_UNDEFINED, 0},
    {"Implode", event_implode, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Smite Evil", event_daily_use_cooldown, EVENT_CHAR,
     "You are once again prepared to smite your evil foes.",
     "One of your smite evil uses has recovered.",
     FEAT_SMITE_EVIL, 0},
    {"Perform", event_countdown, EVENT_CHAR,
     "You are once again prepared to perform.", NULL, FEAT_UNDEFINED, 0},
    /*30*/
    {"Mob Purge", event_countdown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0}, /* Special case handled in code */
    {"SoV Ice Storm", event_ice_storm, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"SoV Chain Lightning", event_chain_lightning, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Darkness", event_countdown, EVENT_ROOM, NULL, NULL, FEAT_UNDEFINED, 0}, /* Special case handled in code */
    {"Magic Food", event_countdown, EVENT_CHAR,
     "You feel able to eat magical food again.", NULL, FEAT_UNDEFINED, 0},
    {"Fisted", event_countdown, EVENT_CHAR,
     "The magic fist holding you in place dissolves into nothingness.", NULL, FEAT_UNDEFINED, 0},
    {"Wait", event_countdown, EVENT_CHAR,
     "You are able to act again.", NULL, FEAT_UNDEFINED, 0},
    {"Turn Undead", event_countdown, EVENT_CHAR,
     "You are able to turn undead again.",
     "One of your turn undead uses has recovered.",
     FEAT_TURN_UNDEAD, 0},
    {"SpellBattle", event_countdown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0}, /* Special case */
    {"Falling", event_falling, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    /*40*/
    {"Check Occupied", event_check_occupied, EVENT_ROOM, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Tracks", event_tracks, EVENT_ROOM, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Wild Shape", event_daily_use_cooldown, EVENT_CHAR,
     "You may assume your wild shape again.",
     "One of your wild shape uses has recovered.",
     FEAT_WILD_SHAPE, 0},
    {"Shield Recovery", event_countdown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Combat Round", event_combat_round, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Standard Action Cooldown", event_action_cooldown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Move Action Cooldown", event_action_cooldown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Wholeness of Body", event_countdown, EVENT_CHAR,
     "You are now able to use Wholeness of Body again.", NULL, FEAT_UNDEFINED, 0},
    {"Empty Body", event_countdown, EVENT_CHAR,
     "You are now able to use Empty Body again.", NULL, FEAT_UNDEFINED, 0},
    {"Quivering Palm", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to strike with your quivering palm again.",
     "One of your quivering palm uses has recovered.",
     FEAT_QUIVERING_PALM, 0},
    /*50*/
    {"Swift Action Cooldown", event_action_cooldown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Trap Triggered", event_trap_triggered, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Suprise Accuracy", event_countdown, EVENT_CHAR,
     "You are now able to use surprise accuracy again.", NULL, FEAT_UNDEFINED, 0},
    {"Powerful Blow", event_countdown, EVENT_CHAR,
     "You are now able to use powerful blow again.", NULL, FEAT_UNDEFINED, 0},
    {"Renewed Vigor", event_countdown, EVENT_CHAR,
     "You are now able to use Renewed Vigor again.", NULL, FEAT_UNDEFINED, 0},
    {"Come and Get Me!", event_countdown, EVENT_CHAR,
     "You are now able to use 'come and get me' again.", NULL, FEAT_UNDEFINED, 0},
    {"Animate Dead", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to animate dead again.",
     "One of your animate dead uses has recovered.",
     FEAT_ANIMATE_DEAD, 0},
    {"Vanish", event_countdown, EVENT_CHAR,
     "Your 'vanished' state returns to normal...", NULL, FEAT_UNDEFINED, 0},
    {"Vanish Cool Down", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to vanish again.",
     "One of your vanish uses has recovered.",
     FEAT_VANISH, 0},
    {"Intimidated", event_countdown, EVENT_CHAR,
     "You feel the effects of the intimidation wear off.", NULL, FEAT_UNDEFINED, 0},
    /*60*/
    {"Intimidated Cool Down", event_countdown, EVENT_CHAR,
     "You are now able to intimidate again.", NULL, FEAT_UNDEFINED, 0},
    {"Lightning Arc Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your lightning arc uses has recovered.",
     FEAT_LIGHTNING_ARC, 0},
    {"Acid Dart Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your acid dart uses has recovered.",
     FEAT_ACID_DART, 0},
    {"Fire Bolt Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your fire bolt uses has recovered.",
     FEAT_FIRE_BOLT, 0},
    {"Icicle Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your icicle uses has recovered.",
     FEAT_ICICLE, 0},
    {"Struggle Cooldown", event_countdown, EVENT_CHAR,
     "You are now able to 'struggle' again.", NULL, FEAT_UNDEFINED, 0}, /* Special: only if grappled */
    {"Curse Touch Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your curse touch uses has recovered.",
     FEAT_CURSE_TOUCH, 0},
    {"Smite Good", event_daily_use_cooldown, EVENT_CHAR,
     "You are once again prepared to smite your good foes.",
     "One of your smite good uses has recovered.",
     FEAT_SMITE_GOOD, 0},
    {"Destructive Smite", event_daily_use_cooldown, EVENT_CHAR,
     "You are once again prepared to smite your foes.",
     "One of your destructive smite uses has recovered.",
     FEAT_DESTRUCTIVE_SMITE, 0},
    {"Destructive Aura", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your destructive aura uses has recovered.",
     FEAT_DESTRUCTIVE_AURA, 0},
    /*70*/
    {"Evil Touch", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your evil touch uses has recovered.",
     FEAT_EVIL_TOUCH, 0},
    {"Good Touch", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your good touch uses has recovered.",
     FEAT_GOOD_TOUCH, 0},
    {"Healing Touch", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your healing touch uses has recovered.",
     FEAT_HEALING_TOUCH, 0},
    {"Eye of Knowledge", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your eye of knowledge uses has recovered.",
     FEAT_EYE_OF_KNOWLEDGE, 0},
    {"Blessed Touch", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your blessed touch uses has recovered.",
     FEAT_BLESSED_TOUCH, 0},
    {"Lawful Weapon", event_daily_use_cooldown, EVENT_CHAR,
     NULL, NULL, FEAT_LAWFUL_WEAPON, 0},
    {"Copycat", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your copycat uses has recovered.",
     FEAT_COPYCAT, 0},
    {"Mass Invis", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your mass invis uses has recovered.",
     FEAT_MASS_INVIS, 0},
    {"Aura of Protection", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your aura of protection uses has recovered.",
     FEAT_AURA_OF_PROTECTION, 0},
    {"Battle Rage", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your battle rage uses has recovered.",
     FEAT_BATTLE_RAGE, 0},
    /*80*/
    {"Crystal fist", event_countdown, EVENT_CHAR,
     "Your body loses its crystal-like properties.", NULL, FEAT_UNDEFINED, 0},
    {"Crystal body", event_countdown, EVENT_CHAR,
     "Your body loses its crystal-like properties.", NULL, FEAT_UNDEFINED, 0},
    {"Bardic Performance", event_bardic_performance, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Encounter Region Reset", event_countdown, EVENT_REGION, NULL, NULL, FEAT_UNDEFINED, 0}, /* Special */
    {"Seeker Arrow", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "You regain a usage of your seeker arrow.",
     FEAT_SEEKER_ARROW, 0},
    {"Imbue Arrow", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "You regain a usage of your imbue arrow.",
     FEAT_IMBUE_ARROW, 0},
    {"Arrow of Death", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to imbue an arrow with death.",
     "You are now able to imbue an arrow with death again.",
     FEAT_ARROW_OF_DEATH, 0},
    {"Swarm of Arrows", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your swarm of arrows again.",
     "One of your swarm of arrows uses has recovered.",
     FEAT_SWARM_OF_ARROWS, 0},
    {"Renewed Defense", event_countdown, EVENT_CHAR,
     "You are now able to use Renewed Defense again.", NULL, FEAT_UNDEFINED, 0},
    {"Last Word", event_countdown, EVENT_CHAR,
     "You are now able to get the 'last word' in again.", NULL, FEAT_UNDEFINED, 0},
    /*90*/
    {"Smash Defense", event_countdown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Defensive Stance", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your Defensive Stance again.",
     "One of your defensive stance uses has recovered.",
     FEAT_DEFENSIVE_STANCE, 0},
    {"Crippled by Critical", event_countdown, EVENT_CHAR,
     "You feel the effects of the crippling critical wear off.", NULL, FEAT_UNDEFINED, 0},
    {"Quest Completed!", event_countdown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0}, /* Special */
    {"Levitate", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your levitate uses has recovered.",
     FEAT_SLA_LEVITATE, 0},
    /*95*/
    {"Darkness", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your darkness uses has recovered.",
     FEAT_SLA_DARKNESS, 0},
    {"Faerie Fire", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your faerie fire uses has recovered.",
     FEAT_SLA_FAERIE_FIRE, 0},
    {"Draconic Heritage Breath Weapon Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your draconic heritage breath weapon uses has recovered.",
     FEAT_DRACONIC_HERITAGE_BREATHWEAPON, 0},
    {"Draconic Heritage Claws Attack Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, NULL, FEAT_DRACONIC_HERITAGE_CLAWS, 0},
    {"Spell Preparation", event_preparation, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    /*100*/
    {"Craft", event_craft, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Copyover Event!", event_copyover, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Autocollect delay", event_countdown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0}, /* Special */
    {"Metamagic Adept Usage Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your metamagic (arcane) adept uses has recovered.",
     FEAT_METAMAGIC_ADEPT, 0},
    {"Armor SpecAb Cooldown: Blinding", event_daily_use_cooldown, EVENT_OBJECT,
     NULL, NULL, FEAT_UNDEFINED, 2}, /* 2 uses per day */
    /*105*/
    {"Item SpecAb Cooldown: Horn of Summoning", event_daily_use_cooldown, EVENT_OBJECT,
     NULL, NULL, FEAT_UNDEFINED, 2}, /* 2 uses per day */
    {"Mutagens/Cognatogens", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to prepare another mutagen or cognatogen again.",
     "One of your mutagens or cognatogens are ready to prepare.",
     FEAT_MUTAGEN, 0},
    {"Curing Touch", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your curing touch uses has recovered.",
     FEAT_CURING_TOUCH, 0},
    {"Psychokinetic Tincture", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to apply a new psychokinetic tincture.",
     "Your psychokinetic tincture is ready to prepare.",
     FEAT_PSYCHOKINETIC, 0},
    {"Impromptu Sneak Attack", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "You regain a usage of your impromptu sneak attack.",
     FEAT_IMPROMPTU_SNEAK_ATTACK, 0},
    /*110*/
    {"Invisible Rogue Cool Down", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your 'invisible rogue' ability again.",
     "One of your invisible rogue uses has recovered.",
     FEAT_INVISIBLE_ROGUE, 0},
    {"Sacred Flames Cool Down", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use Sacred Flames again.",
     "One of your sacred flames uses has recovered.",
     FEAT_SACRED_FLAMES, 0},
    {"Inner Fire Cool Down", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use Inner Fire again.",
     "One of your inner fire uses has recovered.",
     FEAT_INNER_FIRE, 0},
    {"Pixie Dust Cool Down", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your pixie dust uses has recovered.",
     FEAT_PIXIE_DUST, 0},
    {"Efreeti Magic Cool Down", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your efreeti magic uses has recovered.",
     FEAT_EFREETI_MAGIC, 0},
    /*115*/
    {"Dragon Magic Cool Down", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your dragon magic uses has recovered.",
     FEAT_DRAGON_MAGIC, 0},
    {"Strength", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your strength uses has recovered.",
     FEAT_SLA_STRENGTH, 0},
    {"Enlarge", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your enlarge uses has recovered.",
     FEAT_SLA_ENLARGE, 0},
    {"Invis", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your invisbility uses has recovered.",
     FEAT_SLA_INVIS, 0},
    {"Concussive Onslaught", event_concussive_onslaught, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    /*120*/
    {"Channel Spell", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to Channel Spells again.",
     "One of your channel spell uses has recovered.",
     FEAT_CHANNEL_SPELL, 0},
    {"Power Leech", event_power_leech, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Psionic Focus", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to perform another psionic focus.",
     "One of your psionic foci is able to perform.",
     FEAT_PSIONIC_FOCUS, 0},
    {"Double Manifest", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to perform another double manifest.",
     "One of your double manifestations are able to be performed.",
     FEAT_DOUBLE_MANIFEST, 0},
    {"Call Shadow", event_countdown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    /*125*/
    {"Shadow Illusion", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to perform another shadow illusion.",
     "One of your shadow illusions are able to be performed.",
     FEAT_SHADOW_ILLUSION, 0},
    {"Shadow Call", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to perform another shadow call.",
     "One of your shadow calls are able to be performed.",
     FEAT_SHADOW_CALL, 0},
    {"Shadow Jump", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to perform another shadow jump.",
     "One of your shadow jumps are able to be performed.",
     FEAT_SHADOW_JUMP, 0},
    {"Shadow Power", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to perform another shadow power.",
     "One of your shadow powers are able to be performed.",
     FEAT_SHADOW_POWER, 0},
    {"Touch of Corruption", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your touch of corruption again.",
     "One of your touch of corruption uses has recovered.",
     FEAT_TOUCH_OF_CORRUPTION, 0},
    /*130*/
    {"Channel Energy", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to channel energy again.",
     "One of your channel energy uses has recovered.",
     FEAT_CHANNEL_ENERGY, 0},
    {"Lich Touch", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to lich touch again.",
     "One of your lich touch uses has recovered.",
     FEAT_LICH_TOUCH, 0},
    {"Lich Rejuvenation", event_countdown, EVENT_CHAR,
     "Your lich rejuvenation has refreshed.",
     "You are able to lich rejuvenate again!",
     FEAT_LICH_REJUV, 0},
    {"Lich Fear", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to lich fear again.",
     "One of your lich fear uses has recovered.",
     FEAT_LICH_FEAR, 0},
    {"Judgement", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your judgement ability again.",
     "One of your judgement ability uses has recovered.",
     FEAT_JUDGEMENT, 0},
    /*135*/
    {"Bane", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your bane ability again.",
     "One of your bane ability uses has recovered.",
     FEAT_BANE, 0},
    {"True Judgement", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your true judgement ability again.",
     "One of your true judgement ability uses has recovered.",
     FEAT_TRUE_JUDGEMENT, 0},
    {"Spiritual Weapon", event_spiritual_weapon, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Dancing Weapon", event_dancing_weapon, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Holy Javelin", event_holy_javelin, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    /*140*/
    {"Item SpecAb Cooldown: Summon Item", event_daily_use_cooldown, EVENT_OBJECT,
     NULL, NULL, FEAT_UNDEFINED, 1}, /* 1 use per day */
    {"Children of the Night", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to call your children of the night again.",
     "One of your children of the night uses has recovered.",
     FEAT_VAMPIRE_CHILDREN_OF_THE_NIGHT, 0},
    {"Vampiric Energy Drain", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to perform vampiric energy drain with a slam attack again.",
     "One of your vampiric energy drain uses has recovered.",
     FEAT_VAMPIRE_ENERGY_DRAIN, 0},
    {"Master of the Mind", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your mastermind ability again.",
     "One of your mastermind ability uses has recovered.",
     FEAT_MASTER_OF_THE_MIND, 0},
    {"Insect Being", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "You may use insect being again.",
     FEAT_INSECTBEING, 0},
    /*145*/
    {"Blur attack delay", event_countdown, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Tinker ability delay", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your tinker ability again.",
     "One of your tinker ability uses has recovered.",
     FEAT_TINKER, 0},
    {"Moonbeam", event_moonbeam, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Dragonborn Breath Weapon Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your dragonborn ancestry breath weapon uses has recovered.",
     FEAT_DRAGONBORN_BREATH, 0},
    {"Tabaxi Cats Claws Attack Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your tabaxi cat's claws uses has recovered.",
     FEAT_TABAXI_CATS_CLAWS, 0},
    /*150*/
    {"Goliath Stone's Endurance Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to ujse stones endurance again.",
     "One of your stonesendurance ability uses has recovered.",
     FEAT_STONES_ENDURANCE, 0},
    {"Aqueous Orb", event_aqueous_orb, EVENT_CHAR, NULL, NULL, FEAT_UNDEFINED, 0},
    {"Vampiric Blood Drain", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to perform vampiric eblood drain again.",
     "One of your vampiric blood drain uses has recovered.",
     FEAT_VAMPIRE_BLOOD_DRAIN, 0},
    {"Eidolon Breath Weapon Cooldown", event_daily_use_cooldown, EVENT_CHAR,
     NULL, NULL, FEAT_UNDEFINED, 0},
    {"Call Eidolon", event_countdown, EVENT_CHAR,
     "You are now able to 'call eidolon' again.", NULL, FEAT_UNDEFINED, 0},
    {"Touch of Undeath", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your touch of undeath again.",
     "One of your touch of undeath uses has recovered.",
     FEAT_TOUCH_OF_UNDEATH, 0},
    {"Strength of Honor", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your strength of honor again.",
     "One of your strength of honor uses has recovered.",
     FEAT_STRENGTH_OF_HONOR, 0},
    {"Crown of Knighthood", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your crown of knighthood again.",
     "One of your crown of knighthood uses has recovered.",
     FEAT_CROWN_OF_KNIGHTHOOD, 0},
    {"Soul of Knighthood", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to use your soul of knighthood again.",
     "One of your soul of knighthood uses has recovered.",
     FEAT_SOUL_OF_KNIGHTHOOD, 0},
    {"Inspire Courage", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your inspire courage uses has recovered.",
     FEAT_INSPIRE_COURAGE, 0},
    {"Wisdom of the Measure", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your wisdom of the measure uses has recovered.",
     FEAT_WISDOM_OF_THE_MEASURE, 0},
    {"Final Stand", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your final stand uses has recovered.",
     FEAT_FINAL_STAND, 0},
    {"Knighthood's Flower", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your knighthood's flower uses has recovered.",
     FEAT_KNIGHTHOODS_FLOWER, 0},
    {"Rallying Cry", event_daily_use_cooldown, EVENT_CHAR,
     NULL, "One of your rallying cry uses has recovered.",
     FEAT_RALLYING_CRY, 0},
    {"Cosmic Understanding", event_daily_use_cooldown, EVENT_CHAR,
     "You are now able to foretell and prescience again.",
     "One of your foretell/prescience uses has recovered.",
     FEAT_COSMIC_UNDERSTANDING, 0},
    {"Dragoon Points", event_daily_use_cooldown, EVENT_CHAR,
     "You have regained a dragoon point.",
     "One of your dragoon points has recovered.",
     FEAT_DRAGOON_POINTS, 0},
    {"Call Dragon Mount", event_countdown, EVENT_CHAR,
     "You are now able to 'call dragon' again.", NULL, FEAT_UNDEFINED, 0},
};