#! /bin/sh

# Guess values for system-dependent variables and create Makefiles.
# Generated automatically using autoconf version 2.13 
# Copyright (C) 1992, 93, 94, 95, 96 Free Software Foundation, Inc.
#
# This configure script is free software; the Free Software Foundation
# gives unlimited permission to copy, distribute and modify it.

# Defaults:
ac_help=
ac_default_prefix=/usr/local
# Any additions from configure.in:

# Initialize some variables set by options.
# The variables have the same names as the options, with
# dashes changed to underlines.
build=NONE
cache_file=./config.cache
exec_prefix=NONE
host=NONE
no_create=
nonopt=NONE
no_recursion=
prefix=NONE
program_prefix=NONE
program_suffix=NONE
program_transform_name=s,x,x,
silent=
site=
srcdir=
target=NONE
verbose=
x_includes=NONE
x_libraries=NONE
bindir='${exec_prefix}/bin'
sbindir='${exec_prefix}/sbin'
libexecdir='${exec_prefix}/libexec'
datadir='${prefix}/share'
sysconfdir='${prefix}/etc'
sharedstatedir='${prefix}/com'
localstatedir='${prefix}/var'
libdir='${exec_prefix}/lib'
includedir='${prefix}/include'
oldincludedir='/usr/include'
infodir='${prefix}/info'
mandir='${prefix}/man'

# Initialize some other variables.
subdirs=
MFLAGS= MAKEFLAGS=
SHELL=${CONFIG_SHELL-/bin/sh}
# Maximum number of lines to put in a shell here document.
ac_max_here_lines=12

ac_prev=
for ac_option
do

  # If the previous option needs an argument, assign it.
  if test -n "$ac_prev"; then
    eval "$ac_prev=\$ac_option"
    ac_prev=
    continue
  fi

  case "$ac_option" in
  -*=*) ac_optarg=`echo "$ac_option" | sed 's/[-_a-zA-Z0-9]*=//'` ;;
  *) ac_optarg= ;;
  esac

  # Accept the important Cygnus configure options, so we can diagnose typos.

  case "$ac_option" in

  -bindir | --bindir | --bindi | --bind | --bin | --bi)
    ac_prev=bindir ;;
  -bindir=* | --bindir=* | --bindi=* | --bind=* | --bin=* | --bi=*)
    bindir="$ac_optarg" ;;

  -build | --build | --buil | --bui | --bu)
    ac_prev=build ;;
  -build=* | --build=* | --buil=* | --bui=* | --bu=*)
    build="$ac_optarg" ;;

  -cache-file | --cache-file | --cache-fil | --cache-fi \
  | --cache-f | --cache- | --cache | --cach | --cac | --ca | --c)
    ac_prev=cache_file ;;
  -cache-file=* | --cache-file=* | --cache-fil=* | --cache-fi=* \
  | --cache-f=* | --cache-=* | --cache=* | --cach=* | --cac=* | --ca=* | --c=*)
    cache_file="$ac_optarg" ;;

  -datadir | --datadir | --datadi | --datad | --data | --dat | --da)
    ac_prev=datadir ;;
  -datadir=* | --datadir=* | --datadi=* | --datad=* | --data=* | --dat=* \
  | --da=*)
    datadir="$ac_optarg" ;;

  -disable-* | --disable-*)
    ac_feature=`echo $ac_option|sed -e 's/-*disable-//'`
    # Reject names that are not valid shell variable names.
    if test -n "`echo $ac_feature| sed 's/[-a-zA-Z0-9_]//g'`"; then
      { echo "configure: error: $ac_feature: invalid feature name" 1>&2; exit 1; }
    fi
    ac_feature=`echo $ac_feature| sed 's/-/_/g'`
    eval "enable_${ac_feature}=no" ;;

  -enable-* | --enable-*)
    ac_feature=`echo $ac_option|sed -e 's/-*enable-//' -e 's/=.*//'`
    # Reject names that are not valid shell variable names.
    if test -n "`echo $ac_feature| sed 's/[-_a-zA-Z0-9]//g'`"; then
      { echo "configure: error: $ac_feature: invalid feature name" 1>&2; exit 1; }
    fi
    ac_feature=`echo $ac_feature| sed 's/-/_/g'`
    case "$ac_option" in
      *=*) ;;
      *) ac_optarg=yes ;;
    esac
    eval "enable_${ac_feature}='$ac_optarg'" ;;

  -exec-prefix | --exec_prefix | --exec-prefix | --exec-prefi \
  | --exec-pref | --exec-pre | --exec-pr | --exec-p | --exec- \
  | --exec | --exe | --ex)
    ac_prev=exec_prefix ;;
  -exec-prefix=* | --exec_prefix=* | --exec-prefix=* | --exec-prefi=* \
  | --exec-pref=* | --exec-pre=* | --exec-pr=* | --exec-p=* | --exec-=* \
  | --exec=* | --exe=* | --ex=*)
    exec_prefix="$ac_optarg" ;;

  -gas | --gas | --ga | --g)
    # Obsolete; use --with-gas.
    with_gas=yes ;;

  -help | --help | --hel | --he)
    # Omit some internal or obsolete options to make the list less imposing.
    # This message is too long to be a string in the A/UX 3.1 sh.
    cat << EOF
Usage: configure [options] [host]
Options: [defaults in brackets after descriptions]
Configuration:
  --cache-file=FILE       cache test results in FILE
  --help                  print this message
  --no-create             do not create output files
  --quiet, --silent       do not print \`checking...' messages
  --version               print the version of autoconf that created configure
Directory and file names:
  --prefix=PREFIX         install architecture-independent files in PREFIX
                          [$ac_default_prefix]
  --exec-prefix=EPREFIX   install architecture-dependent files in EPREFIX
                          [same as prefix]
  --bindir=DIR            user executables in DIR [EPREFIX/bin]
  --sbindir=DIR           system admin executables in DIR [EPREFIX/sbin]
  --libexecdir=DIR        program executables in DIR [EPREFIX/libexec]
  --datadir=DIR           read-only architecture-independent data in DIR
                          [PREFIX/share]
  --sysconfdir=DIR        read-only single-machine data in DIR [PREFIX/etc]
  --sharedstatedir=DIR    modifiable architecture-independent data in DIR
                          [PREFIX/com]
  --localstatedir=DIR     modifiable single-machine data in DIR [PREFIX/var]
  --libdir=DIR            object code libraries in DIR [EPREFIX/lib]
  --includedir=DIR        C header files in DIR [PREFIX/include]
  --oldincludedir=DIR     C header files for non-gcc in DIR [/usr/include]
  --infodir=DIR           info documentation in DIR [PREFIX/info]
  --mandir=DIR            man documentation in DIR [PREFIX/man]
  --srcdir=DIR            find the sources in DIR [configure dir or ..]
  --program-prefix=PREFIX prepend PREFIX to installed program names
  --program-suffix=SUFFIX append SUFFIX to installed program names
  --program-transform-name=PROGRAM
                          run sed PROGRAM on installed program names
EOF
    cat << EOF
Host type:
  --build=BUILD           configure for building on BUILD [BUILD=HOST]
  --host=HOST             configure for HOST [guessed]
  --target=TARGET         configure for TARGET [TARGET=HOST]
Features and packages:
  --disable-FEATURE       do not include FEATURE (same as --enable-FEATURE=no)
  --enable-FEATURE[=ARG]  include FEATURE [ARG=yes]
  --with-PACKAGE[=ARG]    use PACKAGE [ARG=yes]
  --without-PACKAGE       do not use PACKAGE (same as --with-PACKAGE=no)
  --x-includes=DIR        X include files are in DIR
  --x-libraries=DIR       X library files are in DIR
EOF
    if test -n "$ac_help"; then
      echo "--enable and --with options recognized:$ac_help"
    fi
    exit 0 ;;

  -host | --host | --hos | --ho)
    ac_prev=host ;;
  -host=* | --host=* | --hos=* | --ho=*)
    host="$ac_optarg" ;;

  -includedir | --includedir | --includedi | --included | --include \
  | --includ | --inclu | --incl | --inc)
    ac_prev=includedir ;;
  -includedir=* | --includedir=* | --includedi=* | --included=* | --include=* \
  | --includ=* | --inclu=* | --incl=* | --inc=*)
    includedir="$ac_optarg" ;;

  -infodir | --infodir | --infodi | --infod | --info | --inf)
    ac_prev=infodir ;;
  -infodir=* | --infodir=* | --infodi=* | --infod=* | --info=* | --inf=*)
    infodir="$ac_optarg" ;;

  -libdir | --libdir | --libdi | --libd)
    ac_prev=libdir ;;
  -libdir=* | --libdir=* | --libdi=* | --libd=*)
    libdir="$ac_optarg" ;;

  -libexecdir | --libexecdir | --libexecdi | --libexecd | --libexec \
  | --libexe | --libex | --libe)
    ac_prev=libexecdir ;;
  -libexecdir=* | --libexecdir=* | --libexecdi=* | --libexecd=* | --libexec=* \
  | --libexe=* | --libex=* | --libe=*)
    libexecdir="$ac_optarg" ;;

  -localstatedir | --localstatedir | --localstatedi | --localstated \
  | --localstate | --localstat | --localsta | --localst \
  | --locals | --local | --loca | --loc | --lo)
    ac_prev=localstatedir ;;
  -localstatedir=* | --localstatedir=* | --localstatedi=* | --localstated=* \
  | --localstate=* | --localstat=* | --localsta=* | --localst=* \
  | --locals=* | --local=* | --loca=* | --loc=* | --lo=*)
    localstatedir="$ac_optarg" ;;

  -mandir | --mandir | --mandi | --mand | --man | --ma | --m)
    ac_prev=mandir ;;
  -mandir=* | --mandir=* | --mandi=* | --mand=* | --man=* | --ma=* | --m=*)
    mandir="$ac_optarg" ;;

  -nfp | --nfp | --nf)
    # Obsolete; use --without-fp.
    with_fp=no ;;

  -no-create | --no-create | --no-creat | --no-crea | --no-cre \
  | --no-cr | --no-c)
    no_create=yes ;;

  -no-recursion | --no-recursion | --no-recursio | --no-recursi \
  | --no-recurs | --no-recur | --no-recu | --no-rec | --no-re | --no-r)
    no_recursion=yes ;;

  -oldincludedir | --oldincludedir | --oldincludedi | --oldincluded \
  | --oldinclude | --oldinclud | --oldinclu | --oldincl | --oldinc \
  | --oldin | --oldi | --old | --ol | --o)
    ac_prev=oldincludedir ;;
  -oldincludedir=* | --oldincludedir=* | --oldincludedi=* | --oldincluded=* \
  | --oldinclude=* | --oldinclud=* | --oldinclu=* | --oldincl=* | --oldinc=* \
  | --oldin=* | --oldi=* | --old=* | --ol=* | --o=*)
    oldincludedir="$ac_optarg" ;;

  -prefix | --prefix | --prefi | --pref | --pre | --pr | --p)
    ac_prev=prefix ;;
  -prefix=* | --prefix=* | --prefi=* | --pref=* | --pre=* | --pr=* | --p=*)
    prefix="$ac_optarg" ;;

  -program-prefix | --program-prefix | --program-prefi | --program-pref \
  | --program-pre | --program-pr | --program-p)
    ac_prev=program_prefix ;;
  -program-prefix=* | --program-prefix=* | --program-prefi=* \
  | --program-pref=* | --program-pre=* | --program-pr=* | --program-p=*)
    program_prefix="$ac_optarg" ;;

  -program-suffix | --program-suffix | --program-suffi | --program-suff \
  | --program-suf | --program-su | --program-s)
    ac_prev=program_suffix ;;
  -program-suffix=* | --program-suffix=* | --program-suffi=* \
  | --program-suff=* | --program-suf=* | --program-su=* | --program-s=*)
    program_suffix="$ac_optarg" ;;

  -program-transform-name | --program-transform-name \
  | --program-transform-nam | --program-transform-na \
  | --program-transform-n | --program-transform- \
  | --program-transform | --program-transfor \
  | --program-transfo | --program-transf \
  | --program-trans | --program-tran \
  | --progr-tra | --program-tr | --program-t)
    ac_prev=program_transform_name ;;
  -program-transform-name=* | --program-transform-name=* \
  | --program-transform-nam=* | --program-transform-na=* \
  | --program-transform-n=* | --program-transform-=* \
  | --program-transform=* | --program-transfor=* \
  | --program-transfo=* | --program-transf=* \
  | --program-trans=* | --program-tran=* \
  | --progr-tra=* | --program-tr=* | --program-t=*)
    program_transform_name="$ac_optarg" ;;

  -q | -quiet | --quiet | --quie | --qui | --qu | --q \
  | -silent | --silent | --silen | --sile | --sil)
    silent=yes ;;

  -sbindir | --sbindir | --sbindi | --sbind | --sbin | --sbi | --sb)
    ac_prev=sbindir ;;
  -sbindir=* | --sbindir=* | --sbindi=* | --sbind=* | --sbin=* \
  | --sbi=* | --sb=*)
    sbindir="$ac_optarg" ;;

  -sharedstatedir | --sharedstatedir | --sharedstatedi \
  | --sharedstated | --sharedstate | --sharedstat | --sharedsta \
  | --sharedst | --shareds | --shared | --share | --shar \
  | --sha | --sh)
    ac_prev=sharedstatedir ;;
  -sharedstatedir=* | --sharedstatedir=* | --sharedstatedi=* \
  | --sharedstated=* | --sharedstate=* | --sharedstat=* | --sharedsta=* \
  | --sharedst=* | --shareds=* | --shared=* | --share=* | --shar=* \
  | --sha=* | --sh=*)
    sharedstatedir="$ac_optarg" ;;

  -site | --site | --sit)
    ac_prev=site ;;
  -site=* | --site=* | --sit=*)
    site="$ac_optarg" ;;

  -srcdir | --srcdir | --srcdi | --srcd | --src | --sr)
    ac_prev=srcdir ;;
  -srcdir=* | --srcdir=* | --srcdi=* | --srcd=* | --src=* | --sr=*)
    srcdir="$ac_optarg" ;;

  -sysconfdir | --sysconfdir | --sysconfdi | --sysconfd | --sysconf \
  | --syscon | --sysco | --sysc | --sys | --sy)
    ac_prev=sysconfdir ;;
  -sysconfdir=* | --sysconfdir=* | --sysconfdi=* | --sysconfd=* | --sysconf=* \
  | --syscon=* | --sysco=* | --sysc=* | --sys=* | --sy=*)
    sysconfdir="$ac_optarg" ;;

  -target | --target | --targe | --targ | --tar | --ta | --t)
    ac_prev=target ;;
  -target=* | --target=* | --targe=* | --targ=* | --tar=* | --ta=* | --t=*)
    target="$ac_optarg" ;;

  -v | -verbose | --verbose | --verbos | --verbo | --verb)
    verbose=yes ;;

  -version | --version | --versio | --versi | --vers)
    echo "configure generated by autoconf version 2.13"
    exit 0 ;;

  -with-* | --with-*)
    ac_package=`echo $ac_option|sed -e 's/-*with-//' -e 's/=.*//'`
    # Reject names that are not valid shell variable names.
    if test -n "`echo $ac_package| sed 's/[-_a-zA-Z0-9]//g'`"; then
      { echo "configure: error: $ac_package: invalid package name" 1>&2; exit 1; }
    fi
    ac_package=`echo $ac_package| sed 's/-/_/g'`
    case "$ac_option" in
      *=*) ;;
      *) ac_optarg=yes ;;
    esac
    eval "with_${ac_package}='$ac_optarg'" ;;

  -without-* | --without-*)
    ac_package=`echo $ac_option|sed -e 's/-*without-//'`
    # Reject names that are not valid shell variable names.
    if test -n "`echo $ac_package| sed 's/[-a-zA-Z0-9_]//g'`"; then
      { echo "configure: error: $ac_package: invalid package name" 1>&2; exit 1; }
    fi
    ac_package=`echo $ac_package| sed 's/-/_/g'`
    eval "with_${ac_package}=no" ;;

  --x)
    # Obsolete; use --with-x.
    with_x=yes ;;

  -x-includes | --x-includes | --x-include | --x-includ | --x-inclu \
  | --x-incl | --x-inc | --x-in | --x-i)
    ac_prev=x_includes ;;
  -x-includes=* | --x-includes=* | --x-include=* | --x-includ=* | --x-inclu=* \
  | --x-incl=* | --x-inc=* | --x-in=* | --x-i=*)
    x_includes="$ac_optarg" ;;

  -x-libraries | --x-libraries | --x-librarie | --x-librari \
  | --x-librar | --x-libra | --x-libr | --x-lib | --x-li | --x-l)
    ac_prev=x_libraries ;;
  -x-libraries=* | --x-libraries=* | --x-librarie=* | --x-librari=* \
  | --x-librar=* | --x-libra=* | --x-libr=* | --x-lib=* | --x-li=* | --x-l=*)
    x_libraries="$ac_optarg" ;;

  -*) { echo "configure: error: $ac_option: invalid option; use --help to show usage" 1>&2; exit 1; }
    ;;

  *)
    if test -n "`echo $ac_option| sed 's/[-a-z0-9.]//g'`"; then
      echo "configure: warning: $ac_option: invalid host type" 1>&2
    fi
    if test "x$nonopt" != xNONE; then
      { echo "configure: error: can only configure for one host and one target at a time" 1>&2; exit 1; }
    fi
    nonopt="$ac_option"
    ;;

  esac
done

if test -n "$ac_prev"; then
  { echo "configure: error: missing argument to --`echo $ac_prev | sed 's/_/-/g'`" 1>&2; exit 1; }
fi

trap 'rm -fr conftest* confdefs* core core.* *.core $ac_clean_files; exit 1' 1 2 15

# File descriptor usage:
# 0 standard input
# 1 file creation
# 2 errors and warnings
# 3 some systems may open it to /dev/tty
# 4 used on the Kubota Titan
# 6 checking for... messages and results
# 5 compiler messages saved in config.log
if test "$silent" = yes; then
  exec 6>/dev/null
else
  exec 6>&1
fi
exec 5>./config.log

echo "\
This file contains any messages produced by compilers while
running configure, to aid debugging if configure makes a mistake.
" 1>&5

# Strip out --no-create and --no-recursion so they do not pile up.
# Also quote any args containing shell metacharacters.
ac_configure_args=
for ac_arg
do
  case "$ac_arg" in
  -no-create | --no-create | --no-creat | --no-crea | --no-cre \
  | --no-cr | --no-c) ;;
  -no-recursion | --no-recursion | --no-recursio | --no-recursi \
  | --no-recurs | --no-recur | --no-recu | --no-rec | --no-re | --no-r) ;;
  *" "*|*"	"*|*[\[\]\~\#\$\^\&\*\(\)\{\}\\\|\;\<\>\?]*)
  ac_configure_args="$ac_configure_args '$ac_arg'" ;;
  *) ac_configure_args="$ac_configure_args $ac_arg" ;;
  esac
done

# NLS nuisances.
# Only set these to C if already set.  These must not be set unconditionally
# because not all systems understand e.g. LANG=C (notably SCO).
# Fixing LC_MESSAGES prevents Solaris sh from translating var values in `set'!
# Non-C LC_CTYPE values break the ctype check.
if test "${LANG+set}"   = set; then LANG=C;   export LANG;   fi
if test "${LC_ALL+set}" = set; then LC_ALL=C; export LC_ALL; fi
if test "${LC_MESSAGES+set}" = set; then LC_MESSAGES=C; export LC_MESSAGES; fi
if test "${LC_CTYPE+set}"    = set; then LC_CTYPE=C;    export LC_CTYPE;    fi

# confdefs.h avoids OS command line length limits that DEFS can exceed.
rm -rf conftest* confdefs.h
# AIX cpp loses on an empty file, so make sure it contains at least a newline.
echo > confdefs.h

# A filename unique to this package, relative to the directory that
# configure is in, which we can look for to find out if srcdir is correct.
ac_unique_file=src/act.comm.c

# Find the source files, if location was not specified.
if test -z "$srcdir"; then
  ac_srcdir_defaulted=yes
  # Try the directory containing this script, then its parent.
  ac_prog=$0
  ac_confdir=`echo $ac_prog|sed 's%/[^/][^/]*$%%'`
  test "x$ac_confdir" = "x$ac_prog" && ac_confdir=.
  srcdir=$ac_confdir
  if test ! -r $srcdir/$ac_unique_file; then
    srcdir=..
  fi
else
  ac_srcdir_defaulted=no
fi
if test ! -r $srcdir/$ac_unique_file; then
  if test "$ac_srcdir_defaulted" = yes; then
    { echo "configure: error: can not find sources in $ac_confdir or .." 1>&2; exit 1; }
  else
    { echo "configure: error: can not find sources in $srcdir" 1>&2; exit 1; }
  fi
fi
srcdir=`echo "${srcdir}" | sed 's%\([^/]\)/*$%\1%'`

# Prefer explicitly selected file to automatically selected ones.
if test -z "$CONFIG_SITE"; then
  if test "x$prefix" != xNONE; then
    CONFIG_SITE="$prefix/share/config.site $prefix/etc/config.site"
  else
    CONFIG_SITE="$ac_default_prefix/share/config.site $ac_default_prefix/etc/config.site"
  fi
fi
for ac_site_file in $CONFIG_SITE; do
  if test -r "$ac_site_file"; then
    echo "loading site script $ac_site_file"
    . "$ac_site_file"
  fi
done

if test -r "$cache_file"; then
  echo "loading cache $cache_file"
  . $cache_file
else
  echo "creating cache $cache_file"
  > $cache_file
fi

ac_ext=c
# CFLAGS is not in ac_cpp because -g, -O, etc. are not valid cpp options.
ac_cpp='$CPP $CPPFLAGS'
ac_compile='${CC-cc} -c $CFLAGS $CPPFLAGS conftest.$ac_ext 1>&5'
ac_link='${CC-cc} -o conftest${ac_exeext} $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS 1>&5'
cross_compiling=$ac_cv_prog_cc_cross

ac_exeext=
ac_objext=o
if (echo "testing\c"; echo 1,2,3) | grep c >/dev/null; then
  # Stardent Vistra SVR4 grep lacks -e, says <EMAIL>.
  if (echo -n testing; echo 1,2,3) | sed s/-n/xn/ | grep xn >/dev/null; then
    ac_n= ac_c='
' ac_t='	'
  else
    ac_n=-n ac_c= ac_t=
  fi
else
  ac_n= ac_c='\c' ac_t=
fi







cat >> confdefs.h <<\EOF
#define CIRCLE_UNIX 1
EOF


for ac_prog in less most more cat
do
# Extract the first word of "$ac_prog", so it can be a program name with args.
set dummy $ac_prog; ac_word=$2
echo $ac_n "checking for $ac_word""... $ac_c" 1>&6
echo "configure:540: checking for $ac_word" >&5
if eval "test \"`echo '$''{'ac_cv_prog_MORE'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  if test -n "$MORE"; then
  ac_cv_prog_MORE="$MORE" # Let the user override the test.
else
  IFS="${IFS= 	}"; ac_save_ifs="$IFS"; IFS=":"
  ac_dummy="$PATH"
  for ac_dir in $ac_dummy; do
    test -z "$ac_dir" && ac_dir=.
    if test -f $ac_dir/$ac_word; then
      ac_cv_prog_MORE="$ac_prog"
      break
    fi
  done
  IFS="$ac_save_ifs"
fi
fi
MORE="$ac_cv_prog_MORE"
if test -n "$MORE"; then
  echo "$ac_t""$MORE" 1>&6
else
  echo "$ac_t""no" 1>&6
fi

test -n "$MORE" && break
done


# Extract the first word of "gcc", so it can be a program name with args.
set dummy gcc; ac_word=$2
echo $ac_n "checking for $ac_word""... $ac_c" 1>&6
echo "configure:573: checking for $ac_word" >&5
if eval "test \"`echo '$''{'ac_cv_prog_CC'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
  IFS="${IFS= 	}"; ac_save_ifs="$IFS"; IFS=":"
  ac_dummy="$PATH"
  for ac_dir in $ac_dummy; do
    test -z "$ac_dir" && ac_dir=.
    if test -f $ac_dir/$ac_word; then
      ac_cv_prog_CC="gcc"
      break
    fi
  done
  IFS="$ac_save_ifs"
fi
fi
CC="$ac_cv_prog_CC"
if test -n "$CC"; then
  echo "$ac_t""$CC" 1>&6
else
  echo "$ac_t""no" 1>&6
fi

if test -z "$CC"; then
  # Extract the first word of "cc", so it can be a program name with args.
set dummy cc; ac_word=$2
echo $ac_n "checking for $ac_word""... $ac_c" 1>&6
echo "configure:603: checking for $ac_word" >&5
if eval "test \"`echo '$''{'ac_cv_prog_CC'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
  IFS="${IFS= 	}"; ac_save_ifs="$IFS"; IFS=":"
  ac_prog_rejected=no
  ac_dummy="$PATH"
  for ac_dir in $ac_dummy; do
    test -z "$ac_dir" && ac_dir=.
    if test -f $ac_dir/$ac_word; then
      if test "$ac_dir/$ac_word" = "/usr/ucb/cc"; then
        ac_prog_rejected=yes
	continue
      fi
      ac_cv_prog_CC="cc"
      break
    fi
  done
  IFS="$ac_save_ifs"
if test $ac_prog_rejected = yes; then
  # We found a bogon in the path, so make sure we never use it.
  set dummy $ac_cv_prog_CC
  shift
  if test $# -gt 0; then
    # We chose a different compiler from the bogus one.
    # However, it has the same basename, so the bogon will be chosen
    # first if we set CC to just the basename; use the full file name.
    shift
    set dummy "$ac_dir/$ac_word" "$@"
    shift
    ac_cv_prog_CC="$@"
  fi
fi
fi
fi
CC="$ac_cv_prog_CC"
if test -n "$CC"; then
  echo "$ac_t""$CC" 1>&6
else
  echo "$ac_t""no" 1>&6
fi

  if test -z "$CC"; then
    case "`uname -s`" in
    *win32* | *WIN32*)
      # Extract the first word of "cl", so it can be a program name with args.
set dummy cl; ac_word=$2
echo $ac_n "checking for $ac_word""... $ac_c" 1>&6
echo "configure:654: checking for $ac_word" >&5
if eval "test \"`echo '$''{'ac_cv_prog_CC'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  if test -n "$CC"; then
  ac_cv_prog_CC="$CC" # Let the user override the test.
else
  IFS="${IFS= 	}"; ac_save_ifs="$IFS"; IFS=":"
  ac_dummy="$PATH"
  for ac_dir in $ac_dummy; do
    test -z "$ac_dir" && ac_dir=.
    if test -f $ac_dir/$ac_word; then
      ac_cv_prog_CC="cl"
      break
    fi
  done
  IFS="$ac_save_ifs"
fi
fi
CC="$ac_cv_prog_CC"
if test -n "$CC"; then
  echo "$ac_t""$CC" 1>&6
else
  echo "$ac_t""no" 1>&6
fi
 ;;
    esac
  fi
  test -z "$CC" && { echo "configure: error: no acceptable cc found in \$PATH" 1>&2; exit 1; }
fi

echo $ac_n "checking whether the C compiler ($CC $CFLAGS $LDFLAGS) works""... $ac_c" 1>&6
echo "configure:686: checking whether the C compiler ($CC $CFLAGS $LDFLAGS) works" >&5

ac_ext=c
# CFLAGS is not in ac_cpp because -g, -O, etc. are not valid cpp options.
ac_cpp='$CPP $CPPFLAGS'
ac_compile='${CC-cc} -c $CFLAGS $CPPFLAGS conftest.$ac_ext 1>&5'
ac_link='${CC-cc} -o conftest${ac_exeext} $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS 1>&5'
cross_compiling=$ac_cv_prog_cc_cross

cat > conftest.$ac_ext << EOF

#line 697 "configure"
#include "confdefs.h"

main(){return(0);}
EOF
if { (eval echo configure:702: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  ac_cv_prog_cc_works=yes
  # If we can't run a trivial program, we are probably using a cross compiler.
  if (./conftest; exit) 2>/dev/null; then
    ac_cv_prog_cc_cross=no
  else
    ac_cv_prog_cc_cross=yes
  fi
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  ac_cv_prog_cc_works=no
fi
rm -fr conftest*
ac_ext=c
# CFLAGS is not in ac_cpp because -g, -O, etc. are not valid cpp options.
ac_cpp='$CPP $CPPFLAGS'
ac_compile='${CC-cc} -c $CFLAGS $CPPFLAGS conftest.$ac_ext 1>&5'
ac_link='${CC-cc} -o conftest${ac_exeext} $CFLAGS $CPPFLAGS $LDFLAGS conftest.$ac_ext $LIBS 1>&5'
cross_compiling=$ac_cv_prog_cc_cross

echo "$ac_t""$ac_cv_prog_cc_works" 1>&6
if test $ac_cv_prog_cc_works = no; then
  { echo "configure: error: installation or configuration problem: C compiler cannot create executables." 1>&2; exit 1; }
fi
echo $ac_n "checking whether the C compiler ($CC $CFLAGS $LDFLAGS) is a cross-compiler""... $ac_c" 1>&6
echo "configure:728: checking whether the C compiler ($CC $CFLAGS $LDFLAGS) is a cross-compiler" >&5
echo "$ac_t""$ac_cv_prog_cc_cross" 1>&6
cross_compiling=$ac_cv_prog_cc_cross

echo $ac_n "checking whether we are using GNU C""... $ac_c" 1>&6
echo "configure:733: checking whether we are using GNU C" >&5
if eval "test \"`echo '$''{'ac_cv_prog_gcc'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.c <<EOF
#ifdef __GNUC__
  yes;
#endif
EOF
if { ac_try='${CC-cc} -E conftest.c'; { (eval echo configure:742: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }; } | egrep yes >/dev/null 2>&1; then
  ac_cv_prog_gcc=yes
else
  ac_cv_prog_gcc=no
fi
fi

echo "$ac_t""$ac_cv_prog_gcc" 1>&6

if test $ac_cv_prog_gcc = yes; then
  GCC=yes
else
  GCC=
fi

ac_test_CFLAGS="${CFLAGS+set}"
ac_save_CFLAGS="$CFLAGS"
CFLAGS=
echo $ac_n "checking whether ${CC-cc} accepts -g""... $ac_c" 1>&6
echo "configure:761: checking whether ${CC-cc} accepts -g" >&5
if eval "test \"`echo '$''{'ac_cv_prog_cc_g'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  echo 'void f(){}' > conftest.c
if test -z "`${CC-cc} -g -c conftest.c 2>&1`"; then
  ac_cv_prog_cc_g=yes
else
  ac_cv_prog_cc_g=no
fi
rm -f conftest*

fi

echo "$ac_t""$ac_cv_prog_cc_g" 1>&6
if test "$ac_test_CFLAGS" = set; then
  CFLAGS="$ac_save_CFLAGS"
elif test $ac_cv_prog_cc_g = yes; then
  if test "$GCC" = yes; then
    CFLAGS="-g -O2"
  else
    CFLAGS="-g"
  fi
else
  if test "$GCC" = yes; then
    CFLAGS="-O2"
  else
    CFLAGS=
  fi
fi


if test $ac_cv_prog_gcc = yes; then

    echo $ac_n "checking whether ${CC-cc} -Wall also needs -Wno-char-subscripts""... $ac_c" 1>&6
echo "configure:796: checking whether ${CC-cc} -Wall also needs -Wno-char-subscripts" >&5
if eval "test \"`echo '$''{'ac_cv_char_warn'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -Wall -Werror"
    cat > conftest.$ac_ext <<EOF
#line 804 "configure"
#include "confdefs.h"
#include <ctype.h>
int main() {
 int i; char c = '0';
         i = isascii(c);
         i = isdigit(c);
         i = isprint(c);
       
; return 0; }
EOF
if { (eval echo configure:815: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_char_warn=no
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_char_warn=yes
fi
rm -f conftest*
    CFLAGS=$OLDCFLAGS
  
fi

echo "$ac_t""$ac_cv_char_warn" 1>&6

    echo $ac_n "checking whether ${CC-cc} accepts -Wno-char-subscripts""... $ac_c" 1>&6
echo "configure:832: checking whether ${CC-cc} accepts -Wno-char-subscripts" >&5
if eval "test \"`echo '$''{'ac_cv_gcc_ncs'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -Wno-char-subscripts"
    cat > conftest.$ac_ext <<EOF
#line 840 "configure"
#include "confdefs.h"

int main() {

; return 0; }
EOF
if { (eval echo configure:847: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_gcc_ncs=yes
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_gcc_ncs=no
fi
rm -f conftest*
    CFLAGS=$OLDCFLAGS
  
fi

echo "$ac_t""$ac_cv_gcc_ncs" 1>&6

    echo $ac_n "checking whether ${CC-cc} accepts -fno-builtin""... $ac_c" 1>&6
echo "configure:864: checking whether ${CC-cc} accepts -fno-builtin" >&5
if eval "test \"`echo '$''{'ac_cv_gcc_fnb'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
    cat > conftest.$ac_ext <<EOF
#line 872 "configure"
#include "confdefs.h"

int main() {

; return 0; }
EOF
if { (eval echo configure:879: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_gcc_fnb=yes
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_gcc_fnb=no
fi
rm -f conftest*
    CFLAGS=$OLDCFLAGS
  
fi

echo "$ac_t""$ac_cv_gcc_fnb" 1>&6

        
  if test ${ac_cv_char_warn:-ERROR} = no; then
    MYFLAGS="-Wall"
  else
    if test ${ac_cv_gcc_ncs:-ERROR} = yes; then
      MYFLAGS="-Wall -Wno-char-subscripts"
    else
      MYFLAGS=""
    fi
  fi

else
    MYFLAGS=""

fi

echo $ac_n "checking for gethostbyaddr""... $ac_c" 1>&6
echo "configure:912: checking for gethostbyaddr" >&5
if eval "test \"`echo '$''{'ac_cv_func_gethostbyaddr'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 917 "configure"
#include "confdefs.h"
/* System header to define __stub macros and hopefully few prototypes,
    which can conflict with char gethostbyaddr(); below.  */
#include <assert.h>
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char gethostbyaddr();

int main() {

/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined (__stub_gethostbyaddr) || defined (__stub___gethostbyaddr)
choke me
#else
gethostbyaddr();
#endif

; return 0; }
EOF
if { (eval echo configure:940: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_func_gethostbyaddr=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_func_gethostbyaddr=no"
fi
rm -f conftest*
fi

if eval "test \"`echo '$ac_cv_func_'gethostbyaddr`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  :
else
  echo "$ac_t""no" 1>&6
echo $ac_n "checking for gethostbyaddr in -lnsl""... $ac_c" 1>&6
echo "configure:958: checking for gethostbyaddr in -lnsl" >&5
ac_lib_var=`echo nsl'_'gethostbyaddr | sed 'y%./+-%__p_%'`
if eval "test \"`echo '$''{'ac_cv_lib_$ac_lib_var'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  ac_save_LIBS="$LIBS"
LIBS="-lnsl  $LIBS"
cat > conftest.$ac_ext <<EOF
#line 966 "configure"
#include "confdefs.h"
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char gethostbyaddr();

int main() {
gethostbyaddr()
; return 0; }
EOF
if { (eval echo configure:977: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_lib_$ac_lib_var=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_lib_$ac_lib_var=no"
fi
rm -f conftest*
LIBS="$ac_save_LIBS"

fi
if eval "test \"`echo '$ac_cv_lib_'$ac_lib_var`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  NETLIB="-lnsl $NETLIB"
else
  echo "$ac_t""no" 1>&6
fi

fi


echo $ac_n "checking for socket""... $ac_c" 1>&6
echo "configure:1001: checking for socket" >&5
if eval "test \"`echo '$''{'ac_cv_func_socket'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1006 "configure"
#include "confdefs.h"
/* System header to define __stub macros and hopefully few prototypes,
    which can conflict with char socket(); below.  */
#include <assert.h>
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char socket();

int main() {

/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined (__stub_socket) || defined (__stub___socket)
choke me
#else
socket();
#endif

; return 0; }
EOF
if { (eval echo configure:1029: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_func_socket=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_func_socket=no"
fi
rm -f conftest*
fi

if eval "test \"`echo '$ac_cv_func_'socket`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  :
else
  echo "$ac_t""no" 1>&6
echo $ac_n "checking for socket in -lsocket""... $ac_c" 1>&6
echo "configure:1047: checking for socket in -lsocket" >&5
ac_lib_var=`echo socket'_'socket | sed 'y%./+-%__p_%'`
if eval "test \"`echo '$''{'ac_cv_lib_$ac_lib_var'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  ac_save_LIBS="$LIBS"
LIBS="-lsocket  $LIBS"
cat > conftest.$ac_ext <<EOF
#line 1055 "configure"
#include "confdefs.h"
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char socket();

int main() {
socket()
; return 0; }
EOF
if { (eval echo configure:1066: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_lib_$ac_lib_var=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_lib_$ac_lib_var=no"
fi
rm -f conftest*
LIBS="$ac_save_LIBS"

fi
if eval "test \"`echo '$ac_cv_lib_'$ac_lib_var`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  NETLIB="-lsocket $NETLIB"
else
  echo "$ac_t""no" 1>&6
fi

fi


echo $ac_n "checking for malloc""... $ac_c" 1>&6
echo "configure:1090: checking for malloc" >&5
if eval "test \"`echo '$''{'ac_cv_func_malloc'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1095 "configure"
#include "confdefs.h"
/* System header to define __stub macros and hopefully few prototypes,
    which can conflict with char malloc(); below.  */
#include <assert.h>
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char malloc();

int main() {

/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined (__stub_malloc) || defined (__stub___malloc)
choke me
#else
malloc();
#endif

; return 0; }
EOF
if { (eval echo configure:1118: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_func_malloc=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_func_malloc=no"
fi
rm -f conftest*
fi

if eval "test \"`echo '$ac_cv_func_'malloc`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  :
else
  echo "$ac_t""no" 1>&6
echo $ac_n "checking for malloc in -lmalloc""... $ac_c" 1>&6
echo "configure:1136: checking for malloc in -lmalloc" >&5
ac_lib_var=`echo malloc'_'malloc | sed 'y%./+-%__p_%'`
if eval "test \"`echo '$''{'ac_cv_lib_$ac_lib_var'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  ac_save_LIBS="$LIBS"
LIBS="-lmalloc  $LIBS"
cat > conftest.$ac_ext <<EOF
#line 1144 "configure"
#include "confdefs.h"
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char malloc();

int main() {
malloc()
; return 0; }
EOF
if { (eval echo configure:1155: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_lib_$ac_lib_var=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_lib_$ac_lib_var=no"
fi
rm -f conftest*
LIBS="$ac_save_LIBS"

fi
if eval "test \"`echo '$ac_cv_lib_'$ac_lib_var`\" = yes"; then
  echo "$ac_t""yes" 1>&6
    ac_tr_lib=HAVE_LIB`echo malloc | sed -e 's/[^a-zA-Z0-9_]/_/g' \
    -e 'y/abcdefghijklmnopqrstuvwxyz/ABCDEFGHIJKLMNOPQRSTUVWXYZ/'`
  cat >> confdefs.h <<EOF
#define $ac_tr_lib 1
EOF

  LIBS="-lmalloc $LIBS"

else
  echo "$ac_t""no" 1>&6
fi

fi


echo $ac_n "checking for crypt""... $ac_c" 1>&6
echo "configure:1186: checking for crypt" >&5
if eval "test \"`echo '$''{'ac_cv_func_crypt'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1191 "configure"
#include "confdefs.h"
/* System header to define __stub macros and hopefully few prototypes,
    which can conflict with char crypt(); below.  */
#include <assert.h>
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char crypt();

int main() {

/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined (__stub_crypt) || defined (__stub___crypt)
choke me
#else
crypt();
#endif

; return 0; }
EOF
if { (eval echo configure:1214: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_func_crypt=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_func_crypt=no"
fi
rm -f conftest*
fi

if eval "test \"`echo '$ac_cv_func_'crypt`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  cat >> confdefs.h <<\EOF
#define CIRCLE_CRYPT 1
EOF

else
  echo "$ac_t""no" 1>&6
echo $ac_n "checking for crypt in -lcrypt""... $ac_c" 1>&6
echo "configure:1235: checking for crypt in -lcrypt" >&5
ac_lib_var=`echo crypt'_'crypt | sed 'y%./+-%__p_%'`
if eval "test \"`echo '$''{'ac_cv_lib_$ac_lib_var'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  ac_save_LIBS="$LIBS"
LIBS="-lcrypt  $LIBS"
cat > conftest.$ac_ext <<EOF
#line 1243 "configure"
#include "confdefs.h"
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char crypt();

int main() {
crypt()
; return 0; }
EOF
if { (eval echo configure:1254: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_lib_$ac_lib_var=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_lib_$ac_lib_var=no"
fi
rm -f conftest*
LIBS="$ac_save_LIBS"

fi
if eval "test \"`echo '$ac_cv_lib_'$ac_lib_var`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  cat >> confdefs.h <<\EOF
#define CIRCLE_CRYPT 1
EOF
 CRYPTLIB="-lcrypt"
else
  echo "$ac_t""no" 1>&6
fi

    
fi


echo $ac_n "checking how to run the C preprocessor""... $ac_c" 1>&6
echo "configure:1282: checking how to run the C preprocessor" >&5
# On Suns, sometimes $CPP names a directory.
if test -n "$CPP" && test -d "$CPP"; then
  CPP=
fi
if test -z "$CPP"; then
if eval "test \"`echo '$''{'ac_cv_prog_CPP'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
    # This must be in double quotes, not single quotes, because CPP may get
  # substituted into the Makefile and "${CC-cc}" will confuse make.
  CPP="${CC-cc} -E"
  # On the NeXT, cc -E runs the code through the compiler's parser,
  # not just through cpp.
  cat > conftest.$ac_ext <<EOF
#line 1297 "configure"
#include "confdefs.h"
#include <assert.h>
Syntax Error
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1303: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  :
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  CPP="${CC-cc} -E -traditional-cpp"
  cat > conftest.$ac_ext <<EOF
#line 1314 "configure"
#include "confdefs.h"
#include <assert.h>
Syntax Error
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1320: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  :
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  CPP="${CC-cc} -nologo -E"
  cat > conftest.$ac_ext <<EOF
#line 1331 "configure"
#include "confdefs.h"
#include <assert.h>
Syntax Error
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1337: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  :
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  CPP=/lib/cpp
fi
rm -f conftest*
fi
rm -f conftest*
fi
rm -f conftest*
  ac_cv_prog_CPP="$CPP"
fi
  CPP="$ac_cv_prog_CPP"
else
  ac_cv_prog_CPP="$CPP"
fi
echo "$ac_t""$CPP" 1>&6

echo $ac_n "checking for ANSI C header files""... $ac_c" 1>&6
echo "configure:1362: checking for ANSI C header files" >&5
if eval "test \"`echo '$''{'ac_cv_header_stdc'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1367 "configure"
#include "confdefs.h"
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <float.h>
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1375: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  rm -rf conftest*
  ac_cv_header_stdc=yes
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_header_stdc=no
fi
rm -f conftest*

if test $ac_cv_header_stdc = yes; then
  # SunOS 4.x string.h does not declare mem*, contrary to ANSI.
cat > conftest.$ac_ext <<EOF
#line 1392 "configure"
#include "confdefs.h"
#include <string.h>
EOF
if (eval "$ac_cpp conftest.$ac_ext") 2>&5 |
  egrep "memchr" >/dev/null 2>&1; then
  :
else
  rm -rf conftest*
  ac_cv_header_stdc=no
fi
rm -f conftest*

fi

if test $ac_cv_header_stdc = yes; then
  # ISC 2.0.2 stdlib.h does not declare free, contrary to ANSI.
cat > conftest.$ac_ext <<EOF
#line 1410 "configure"
#include "confdefs.h"
#include <stdlib.h>
EOF
if (eval "$ac_cpp conftest.$ac_ext") 2>&5 |
  egrep "free" >/dev/null 2>&1; then
  :
else
  rm -rf conftest*
  ac_cv_header_stdc=no
fi
rm -f conftest*

fi

if test $ac_cv_header_stdc = yes; then
  # /bin/cc in Irix-4.0.5 gets non-ANSI ctype macros unless using -ansi.
if test "$cross_compiling" = yes; then
  :
else
  cat > conftest.$ac_ext <<EOF
#line 1431 "configure"
#include "confdefs.h"
#include <ctype.h>
#define ISLOWER(c) ('a' <= (c) && (c) <= 'z')
#define TOUPPER(c) (ISLOWER(c) ? 'A' + ((c) - 'a') : (c))
#define XOR(e, f) (((e) && !(f)) || (!(e) && (f)))
int main () { int i; for (i = 0; i < 256; i++)
if (XOR (islower (i), ISLOWER (i)) || toupper (i) != TOUPPER (i)) exit(2);
exit (0); }

EOF
if { (eval echo configure:1442: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext} && (./conftest; exit) 2>/dev/null
then
  :
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -fr conftest*
  ac_cv_header_stdc=no
fi
rm -fr conftest*
fi

fi
fi

echo "$ac_t""$ac_cv_header_stdc" 1>&6
if test $ac_cv_header_stdc = yes; then
  cat >> confdefs.h <<\EOF
#define STDC_HEADERS 1
EOF

fi

echo $ac_n "checking for sys/wait.h that is POSIX.1 compatible""... $ac_c" 1>&6
echo "configure:1466: checking for sys/wait.h that is POSIX.1 compatible" >&5
if eval "test \"`echo '$''{'ac_cv_header_sys_wait_h'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1471 "configure"
#include "confdefs.h"
#include <sys/types.h>
#include <sys/wait.h>
#ifndef WEXITSTATUS
#define WEXITSTATUS(stat_val) ((unsigned)(stat_val) >> 8)
#endif
#ifndef WIFEXITED
#define WIFEXITED(stat_val) (((stat_val) & 255) == 0)
#endif
int main() {
int s;
wait (&s);
s = WIFEXITED (s) ? WEXITSTATUS (s) : 1;
; return 0; }
EOF
if { (eval echo configure:1487: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_header_sys_wait_h=yes
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_header_sys_wait_h=no
fi
rm -f conftest*
fi

echo "$ac_t""$ac_cv_header_sys_wait_h" 1>&6
if test $ac_cv_header_sys_wait_h = yes; then
  cat >> confdefs.h <<\EOF
#define HAVE_SYS_WAIT_H 1
EOF

fi

for ac_hdr in fcntl.h sys/fcntl.h errno.h net/errno.h string.h strings.h
do
ac_safe=`echo "$ac_hdr" | sed 'y%./+-%__p_%'`
echo $ac_n "checking for $ac_hdr""... $ac_c" 1>&6
echo "configure:1511: checking for $ac_hdr" >&5
if eval "test \"`echo '$''{'ac_cv_header_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1516 "configure"
#include "confdefs.h"
#include <$ac_hdr>
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1521: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=yes"
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=no"
fi
rm -f conftest*
fi
if eval "test \"`echo '$ac_cv_header_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
    ac_tr_hdr=HAVE_`echo $ac_hdr | sed 'y%abcdefghijklmnopqrstuvwxyz./-%ABCDEFGHIJKLMNOPQRSTUVWXYZ___%'`
  cat >> confdefs.h <<EOF
#define $ac_tr_hdr 1
EOF
 
else
  echo "$ac_t""no" 1>&6
fi
done

for ac_hdr in limits.h sys/time.h sys/select.h sys/types.h unistd.h
do
ac_safe=`echo "$ac_hdr" | sed 'y%./+-%__p_%'`
echo $ac_n "checking for $ac_hdr""... $ac_c" 1>&6
echo "configure:1551: checking for $ac_hdr" >&5
if eval "test \"`echo '$''{'ac_cv_header_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1556 "configure"
#include "confdefs.h"
#include <$ac_hdr>
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1561: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=yes"
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=no"
fi
rm -f conftest*
fi
if eval "test \"`echo '$ac_cv_header_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
    ac_tr_hdr=HAVE_`echo $ac_hdr | sed 'y%abcdefghijklmnopqrstuvwxyz./-%ABCDEFGHIJKLMNOPQRSTUVWXYZ___%'`
  cat >> confdefs.h <<EOF
#define $ac_tr_hdr 1
EOF
 
else
  echo "$ac_t""no" 1>&6
fi
done

for ac_hdr in memory.h crypt.h assert.h arpa/telnet.h arpa/inet.h
do
ac_safe=`echo "$ac_hdr" | sed 'y%./+-%__p_%'`
echo $ac_n "checking for $ac_hdr""... $ac_c" 1>&6
echo "configure:1591: checking for $ac_hdr" >&5
if eval "test \"`echo '$''{'ac_cv_header_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1596 "configure"
#include "confdefs.h"
#include <$ac_hdr>
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1601: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=yes"
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=no"
fi
rm -f conftest*
fi
if eval "test \"`echo '$ac_cv_header_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
    ac_tr_hdr=HAVE_`echo $ac_hdr | sed 'y%abcdefghijklmnopqrstuvwxyz./-%ABCDEFGHIJKLMNOPQRSTUVWXYZ___%'`
  cat >> confdefs.h <<EOF
#define $ac_tr_hdr 1
EOF
 
else
  echo "$ac_t""no" 1>&6
fi
done

for ac_hdr in sys/stat.h sys/socket.h sys/resource.h netinet/in.h netdb.h
do
ac_safe=`echo "$ac_hdr" | sed 'y%./+-%__p_%'`
echo $ac_n "checking for $ac_hdr""... $ac_c" 1>&6
echo "configure:1631: checking for $ac_hdr" >&5
if eval "test \"`echo '$''{'ac_cv_header_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1636 "configure"
#include "confdefs.h"
#include <$ac_hdr>
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1641: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=yes"
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=no"
fi
rm -f conftest*
fi
if eval "test \"`echo '$ac_cv_header_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
    ac_tr_hdr=HAVE_`echo $ac_hdr | sed 'y%abcdefghijklmnopqrstuvwxyz./-%ABCDEFGHIJKLMNOPQRSTUVWXYZ___%'`
  cat >> confdefs.h <<EOF
#define $ac_tr_hdr 1
EOF
 
else
  echo "$ac_t""no" 1>&6
fi
done

for ac_hdr in signal.h sys/uio.h mcheck.h
do
ac_safe=`echo "$ac_hdr" | sed 'y%./+-%__p_%'`
echo $ac_n "checking for $ac_hdr""... $ac_c" 1>&6
echo "configure:1671: checking for $ac_hdr" >&5
if eval "test \"`echo '$''{'ac_cv_header_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1676 "configure"
#include "confdefs.h"
#include <$ac_hdr>
EOF
ac_try="$ac_cpp conftest.$ac_ext >/dev/null 2>conftest.out"
{ (eval echo configure:1681: \"$ac_try\") 1>&5; (eval $ac_try) 2>&5; }
ac_err=`grep -v '^ *+' conftest.out | grep -v "^conftest.${ac_ext}\$"`
if test -z "$ac_err"; then
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=yes"
else
  echo "$ac_err" >&5
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_header_$ac_safe=no"
fi
rm -f conftest*
fi
if eval "test \"`echo '$ac_cv_header_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
    ac_tr_hdr=HAVE_`echo $ac_hdr | sed 'y%abcdefghijklmnopqrstuvwxyz./-%ABCDEFGHIJKLMNOPQRSTUVWXYZ___%'`
  cat >> confdefs.h <<EOF
#define $ac_tr_hdr 1
EOF
 
else
  echo "$ac_t""no" 1>&6
fi
done



  echo $ac_n "checking whether crypt needs over 10 characters""... $ac_c" 1>&6
echo "configure:1710: checking whether crypt needs over 10 characters" >&5
if eval "test \"`echo '$''{'ac_cv_unsafe_crypt'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  
    if test ${ac_cv_header_crypt_h-no} = yes; then
      use_crypt_header="#include <crypt.h>"
    fi
    if test ${ac_cv_lib_crypt_crypt-no} = yes; then
      ORIGLIBS=$LIBS
      LIBS="-lcrypt $LIBS"
    fi
    if test "$cross_compiling" = yes; then
  ac_cv_unsafe_crypt=no
else
  cat > conftest.$ac_ext <<EOF
#line 1726 "configure"
#include "confdefs.h"

#define _XOPEN_SOURCE
#include <string.h>
#include <unistd.h>
$use_crypt_header

int main(void)
{
  char pwd[11], pwd2[11];

  strncpy(pwd, (char *)crypt("FooBar", "BazQux"), 10);
  pwd[10] = '\0';
  strncpy(pwd2, (char *)crypt("xyzzy", "BazQux"), 10);
  pwd2[10] = '\0';
  if (strcmp(pwd, pwd2) == 0)
    exit(0);
  exit(1);
}


EOF
if { (eval echo configure:1749: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext} && (./conftest; exit) 2>/dev/null
then
  ac_cv_unsafe_crypt=yes
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -fr conftest*
  ac_cv_unsafe_crypt=no
fi
rm -fr conftest*
fi

fi

echo "$ac_t""$ac_cv_unsafe_crypt" 1>&6
if test $ac_cv_unsafe_crypt = yes; then
  cat >> confdefs.h <<\EOF
#define HAVE_UNSAFE_CRYPT 1
EOF

fi
if test ${ac_cv_lib_crypt_crypt-no} = yes; then
  LIBS=$ORIGLIBS
fi


echo $ac_n "checking for working const""... $ac_c" 1>&6
echo "configure:1776: checking for working const" >&5
if eval "test \"`echo '$''{'ac_cv_c_const'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1781 "configure"
#include "confdefs.h"

int main() {

/* Ultrix mips cc rejects this.  */
typedef int charset[2]; const charset x;
/* SunOS 4.1.1 cc rejects this.  */
char const *const *ccp;
char **p;
/* NEC SVR4.0.2 mips cc rejects this.  */
struct point {int x, y;};
static struct point const zero = {0,0};
/* AIX XL C ******** rejects this.
   It does not let you subtract one const X* pointer from another in an arm
   of an if-expression whose if-part is not a constant expression */
const char *g = "string";
ccp = &g + (g ? g-g : 0);
/* HPUX 7.0 cc rejects these. */
++ccp;
p = (char**) ccp;
ccp = (char const *const *) p;
{ /* SCO 3.2v4 cc rejects this.  */
  char *t;
  char const *s = 0 ? (char *) 0 : (char const *) 0;

  *t++ = 0;
}
{ /* Someone thinks the Sun supposedly-ANSI compiler will reject this.  */
  int x[] = {25, 17};
  const int *foo = &x[0];
  ++foo;
}
{ /* Sun SC1.0 ANSI compiler rejects this -- but not the above. */
  typedef const int *iptr;
  iptr p = 0;
  ++p;
}
{ /* AIX XL C ******** rejects this saying
     "k.c", line 2.27: 1506-025 (S) Operand must be a modifiable lvalue. */
  struct s { int j; const int *ap[3]; };
  struct s *b; b->j = 5;
}
{ /* ULTRIX-32 V3.1 (Rev 9) vcc rejects this */
  const int foo = 10;
}

; return 0; }
EOF
if { (eval echo configure:1830: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_c_const=yes
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_c_const=no
fi
rm -f conftest*
fi

echo "$ac_t""$ac_cv_c_const" 1>&6
if test $ac_cv_c_const = no; then
  cat >> confdefs.h <<\EOF
#define const 
EOF

fi

echo $ac_n "checking for pid_t""... $ac_c" 1>&6
echo "configure:1851: checking for pid_t" >&5
if eval "test \"`echo '$''{'ac_cv_type_pid_t'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1856 "configure"
#include "confdefs.h"
#include <sys/types.h>
#if STDC_HEADERS
#include <stdlib.h>
#include <stddef.h>
#endif
EOF
if (eval "$ac_cpp conftest.$ac_ext") 2>&5 |
  egrep "(^|[^a-zA-Z_0-9])pid_t[^a-zA-Z_0-9]" >/dev/null 2>&1; then
  rm -rf conftest*
  ac_cv_type_pid_t=yes
else
  rm -rf conftest*
  ac_cv_type_pid_t=no
fi
rm -f conftest*

fi
echo "$ac_t""$ac_cv_type_pid_t" 1>&6
if test $ac_cv_type_pid_t = no; then
  cat >> confdefs.h <<\EOF
#define pid_t int
EOF

fi

echo $ac_n "checking for size_t""... $ac_c" 1>&6
echo "configure:1884: checking for size_t" >&5
if eval "test \"`echo '$''{'ac_cv_type_size_t'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1889 "configure"
#include "confdefs.h"
#include <sys/types.h>
#if STDC_HEADERS
#include <stdlib.h>
#include <stddef.h>
#endif
EOF
if (eval "$ac_cpp conftest.$ac_ext") 2>&5 |
  egrep "(^|[^a-zA-Z_0-9])size_t[^a-zA-Z_0-9]" >/dev/null 2>&1; then
  rm -rf conftest*
  ac_cv_type_size_t=yes
else
  rm -rf conftest*
  ac_cv_type_size_t=no
fi
rm -f conftest*

fi
echo "$ac_t""$ac_cv_type_size_t" 1>&6
if test $ac_cv_type_size_t = no; then
  cat >> confdefs.h <<\EOF
#define size_t unsigned
EOF

fi

echo $ac_n "checking for ssize_t""... $ac_c" 1>&6
echo "configure:1917: checking for ssize_t" >&5
if eval "test \"`echo '$''{'ac_cv_type_ssize_t'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1922 "configure"
#include "confdefs.h"
#include <sys/types.h>
#if STDC_HEADERS
#include <stdlib.h>
#include <stddef.h>
#endif
EOF
if (eval "$ac_cpp conftest.$ac_ext") 2>&5 |
  egrep "(^|[^a-zA-Z_0-9])ssize_t[^a-zA-Z_0-9]" >/dev/null 2>&1; then
  rm -rf conftest*
  ac_cv_type_ssize_t=yes
else
  rm -rf conftest*
  ac_cv_type_ssize_t=no
fi
rm -f conftest*

fi
echo "$ac_t""$ac_cv_type_ssize_t" 1>&6
if test $ac_cv_type_ssize_t = no; then
  cat >> confdefs.h <<\EOF
#define ssize_t int
EOF

fi

echo $ac_n "checking whether time.h and sys/time.h may both be included""... $ac_c" 1>&6
echo "configure:1950: checking whether time.h and sys/time.h may both be included" >&5
if eval "test \"`echo '$''{'ac_cv_header_time'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 1955 "configure"
#include "confdefs.h"
#include <sys/types.h>
#include <sys/time.h>
#include <time.h>
int main() {
struct tm *tp;
; return 0; }
EOF
if { (eval echo configure:1964: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_header_time=yes
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_header_time=no
fi
rm -f conftest*
fi

echo "$ac_t""$ac_cv_header_time" 1>&6
if test $ac_cv_header_time = yes; then
  cat >> confdefs.h <<\EOF
#define TIME_WITH_SYS_TIME 1
EOF

fi


if test $ac_cv_header_netinet_in_h = no; then
  ac_cv_struct_in_addr = no
else
  if test $ac_cv_header_sys_types_h = yes; then
    headers=`cat << EOF
#include <sys/types.h>
#include <netinet/in.h>
EOF
`
  else
    headers="#include <netinet/in.h>"
  fi

  echo $ac_n "checking for struct in_addr""... $ac_c" 1>&6
echo "configure:1999: checking for struct in_addr" >&5
if eval "test \"`echo '$''{'ac_cv_struct_in_addr'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
   cat > conftest.$ac_ext <<EOF
#line 2004 "configure"
#include "confdefs.h"
$headers
int main() {
struct in_addr tp; tp.s_addr;
; return 0; }
EOF
if { (eval echo configure:2011: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_struct_in_addr=yes
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_struct_in_addr=no
fi
rm -f conftest*
fi

echo "$ac_t""$ac_cv_struct_in_addr" 1>&6

  if test $ac_cv_struct_in_addr = yes; then
    cat >> confdefs.h <<\EOF
#define HAVE_STRUCT_IN_ADDR 1
EOF

  fi
fi


if test $ac_cv_header_sys_socket_h = no; then
  ac_cv_socklen_t = no;
else
  echo $ac_n "checking for typedef socklen_t""... $ac_c" 1>&6
echo "configure:2038: checking for typedef socklen_t" >&5
if eval "test \"`echo '$''{'ac_cv_socklen_t'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
   cat > conftest.$ac_ext <<EOF
#line 2043 "configure"
#include "confdefs.h"
#include <sys/socket.h>
int main() {
socklen_t sl; sl=0;
; return 0; }
EOF
if { (eval echo configure:2050: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_socklen_t=yes
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_socklen_t=no
fi
rm -f conftest*
fi

echo "$ac_t""$ac_cv_socklen_t" 1>&6
fi

if test $ac_cv_socklen_t = no; then
  cat >> confdefs.h <<\EOF
#define socklen_t int
EOF

fi


echo $ac_n "checking return type of signal handlers""... $ac_c" 1>&6
echo "configure:2074: checking return type of signal handlers" >&5
if eval "test \"`echo '$''{'ac_cv_type_signal'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 2079 "configure"
#include "confdefs.h"
#include <sys/types.h>
#include <signal.h>
#ifdef signal
#undef signal
#endif
#ifdef __cplusplus
extern "C" void (*signal (int, void (*)(int)))(int);
#else
void (*signal ()) ();
#endif

int main() {
int i;
; return 0; }
EOF
if { (eval echo configure:2096: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  ac_cv_type_signal=void
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  ac_cv_type_signal=int
fi
rm -f conftest*
fi

echo "$ac_t""$ac_cv_type_signal" 1>&6
cat >> confdefs.h <<EOF
#define RETSIGTYPE $ac_cv_type_signal
EOF


echo $ac_n "checking for vprintf""... $ac_c" 1>&6
echo "configure:2115: checking for vprintf" >&5
if eval "test \"`echo '$''{'ac_cv_func_vprintf'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 2120 "configure"
#include "confdefs.h"
/* System header to define __stub macros and hopefully few prototypes,
    which can conflict with char vprintf(); below.  */
#include <assert.h>
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char vprintf();

int main() {

/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined (__stub_vprintf) || defined (__stub___vprintf)
choke me
#else
vprintf();
#endif

; return 0; }
EOF
if { (eval echo configure:2143: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_func_vprintf=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_func_vprintf=no"
fi
rm -f conftest*
fi

if eval "test \"`echo '$ac_cv_func_'vprintf`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  cat >> confdefs.h <<\EOF
#define HAVE_VPRINTF 1
EOF

else
  echo "$ac_t""no" 1>&6
fi

if test "$ac_cv_func_vprintf" != yes; then
echo $ac_n "checking for _doprnt""... $ac_c" 1>&6
echo "configure:2167: checking for _doprnt" >&5
if eval "test \"`echo '$''{'ac_cv_func__doprnt'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 2172 "configure"
#include "confdefs.h"
/* System header to define __stub macros and hopefully few prototypes,
    which can conflict with char _doprnt(); below.  */
#include <assert.h>
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char _doprnt();

int main() {

/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined (__stub__doprnt) || defined (__stub____doprnt)
choke me
#else
_doprnt();
#endif

; return 0; }
EOF
if { (eval echo configure:2195: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_func__doprnt=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_func__doprnt=no"
fi
rm -f conftest*
fi

if eval "test \"`echo '$ac_cv_func_'_doprnt`\" = yes"; then
  echo "$ac_t""yes" 1>&6
  cat >> confdefs.h <<\EOF
#define HAVE_DOPRNT 1
EOF

else
  echo "$ac_t""no" 1>&6
fi

fi

for ac_func in gettimeofday select snprintf strcasecmp strdup strerror stricmp strlcpy strncasecmp strnicmp strstr vsnprintf
do
echo $ac_n "checking for $ac_func""... $ac_c" 1>&6
echo "configure:2222: checking for $ac_func" >&5
if eval "test \"`echo '$''{'ac_cv_func_$ac_func'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 2227 "configure"
#include "confdefs.h"
/* System header to define __stub macros and hopefully few prototypes,
    which can conflict with char $ac_func(); below.  */
#include <assert.h>
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char $ac_func();

int main() {

/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined (__stub_$ac_func) || defined (__stub___$ac_func)
choke me
#else
$ac_func();
#endif

; return 0; }
EOF
if { (eval echo configure:2250: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_func_$ac_func=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_func_$ac_func=no"
fi
rm -f conftest*
fi

if eval "test \"`echo '$ac_cv_func_'$ac_func`\" = yes"; then
  echo "$ac_t""yes" 1>&6
    ac_tr_func=HAVE_`echo $ac_func | tr 'abcdefghijklmnopqrstuvwxyz' 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'`
  cat >> confdefs.h <<EOF
#define $ac_tr_func 1
EOF
 
else
  echo "$ac_t""no" 1>&6
fi
done


ORIGLIBS=$LIBS
LIBS="$LIBS $NETLIB"
for ac_func in inet_addr inet_aton
do
echo $ac_n "checking for $ac_func""... $ac_c" 1>&6
echo "configure:2280: checking for $ac_func" >&5
if eval "test \"`echo '$''{'ac_cv_func_$ac_func'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  cat > conftest.$ac_ext <<EOF
#line 2285 "configure"
#include "confdefs.h"
/* System header to define __stub macros and hopefully few prototypes,
    which can conflict with char $ac_func(); below.  */
#include <assert.h>
/* Override any gcc2 internal prototype to avoid an error.  */
/* We use char because int might match the return type of a gcc2
    builtin and then its argument prototype would still apply.  */
char $ac_func();

int main() {

/* The GNU C library defines this for functions which it implements
    to always fail with ENOSYS.  Some functions are actually named
    something starting with __ and the normal name is an alias.  */
#if defined (__stub_$ac_func) || defined (__stub___$ac_func)
choke me
#else
$ac_func();
#endif

; return 0; }
EOF
if { (eval echo configure:2308: \"$ac_link\") 1>&5; (eval $ac_link) 2>&5; } && test -s conftest${ac_exeext}; then
  rm -rf conftest*
  eval "ac_cv_func_$ac_func=yes"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_func_$ac_func=no"
fi
rm -f conftest*
fi

if eval "test \"`echo '$ac_cv_func_'$ac_func`\" = yes"; then
  echo "$ac_t""yes" 1>&6
    ac_tr_func=HAVE_`echo $ac_func | tr 'abcdefghijklmnopqrstuvwxyz' 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'`
  cat >> confdefs.h <<EOF
#define $ac_tr_func 1
EOF
 
else
  echo "$ac_t""no" 1>&6
fi
done

LIBS=$ORIGLIBS


ac_safe=accept;

echo $ac_n "checking if accept is prototyped""... $ac_c" 1>&6
echo "configure:2338: checking if accept is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2348 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef accept
  error - already defined!
#endif
void accept(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2364: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_ACCEPT_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=atoi;

echo $ac_n "checking if atoi is prototyped""... $ac_c" 1>&6
echo "configure:2395: checking if atoi is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2405 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef atoi
  error - already defined!
#endif
void atoi(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2421: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_ATOI_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=atol;

echo $ac_n "checking if atol is prototyped""... $ac_c" 1>&6
echo "configure:2452: checking if atol is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2462 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef atol
  error - already defined!
#endif
void atol(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2478: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_ATOL_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=bind;

echo $ac_n "checking if bind is prototyped""... $ac_c" 1>&6
echo "configure:2509: checking if bind is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2519 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef bind
  error - already defined!
#endif
void bind(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2535: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_BIND_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=bzero;

echo $ac_n "checking if bzero is prototyped""... $ac_c" 1>&6
echo "configure:2566: checking if bzero is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2576 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef bzero
  error - already defined!
#endif
void bzero(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2592: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_BZERO_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=chdir;

echo $ac_n "checking if chdir is prototyped""... $ac_c" 1>&6
echo "configure:2623: checking if chdir is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2633 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef chdir
  error - already defined!
#endif
void chdir(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2649: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_CHDIR_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=close;

echo $ac_n "checking if close is prototyped""... $ac_c" 1>&6
echo "configure:2680: checking if close is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2690 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef close
  error - already defined!
#endif
void close(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2706: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_CLOSE_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=crypt;

echo $ac_n "checking if crypt is prototyped""... $ac_c" 1>&6
echo "configure:2737: checking if crypt is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2747 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef crypt
  error - already defined!
#endif
void crypt(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2763: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_CRYPT_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fclose;

echo $ac_n "checking if fclose is prototyped""... $ac_c" 1>&6
echo "configure:2794: checking if fclose is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2804 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fclose
  error - already defined!
#endif
void fclose(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2820: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FCLOSE_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fcntl;

echo $ac_n "checking if fcntl is prototyped""... $ac_c" 1>&6
echo "configure:2851: checking if fcntl is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2861 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fcntl
  error - already defined!
#endif
void fcntl(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2877: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FCNTL_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fflush;

echo $ac_n "checking if fflush is prototyped""... $ac_c" 1>&6
echo "configure:2908: checking if fflush is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2918 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fflush
  error - already defined!
#endif
void fflush(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2934: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FFLUSH_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fprintf;

echo $ac_n "checking if fprintf is prototyped""... $ac_c" 1>&6
echo "configure:2965: checking if fprintf is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 2975 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fprintf
  error - already defined!
#endif
void fprintf(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:2991: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FPRINTF_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fputc;

echo $ac_n "checking if fputc is prototyped""... $ac_c" 1>&6
echo "configure:3022: checking if fputc is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3032 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fputc
  error - already defined!
#endif
void fputc(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3048: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FPUTC_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fputs;

echo $ac_n "checking if fputs is prototyped""... $ac_c" 1>&6
echo "configure:3079: checking if fputs is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3089 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fputs
  error - already defined!
#endif
void fputs(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3105: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FPUTS_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fread;

echo $ac_n "checking if fread is prototyped""... $ac_c" 1>&6
echo "configure:3136: checking if fread is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3146 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fread
  error - already defined!
#endif
void fread(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3162: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FREAD_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fscanf;

echo $ac_n "checking if fscanf is prototyped""... $ac_c" 1>&6
echo "configure:3193: checking if fscanf is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3203 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fscanf
  error - already defined!
#endif
void fscanf(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3219: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FSCANF_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fseek;

echo $ac_n "checking if fseek is prototyped""... $ac_c" 1>&6
echo "configure:3250: checking if fseek is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3260 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fseek
  error - already defined!
#endif
void fseek(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3276: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FSEEK_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=fwrite;

echo $ac_n "checking if fwrite is prototyped""... $ac_c" 1>&6
echo "configure:3307: checking if fwrite is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3317 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef fwrite
  error - already defined!
#endif
void fwrite(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3333: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_FWRITE_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=getpeername;

echo $ac_n "checking if getpeername is prototyped""... $ac_c" 1>&6
echo "configure:3364: checking if getpeername is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3374 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef getpeername
  error - already defined!
#endif
void getpeername(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3390: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_GETPEERNAME_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=getpid;

echo $ac_n "checking if getpid is prototyped""... $ac_c" 1>&6
echo "configure:3421: checking if getpid is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3431 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef getpid
  error - already defined!
#endif
void getpid(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3447: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_GETPID_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=getrlimit;

echo $ac_n "checking if getrlimit is prototyped""... $ac_c" 1>&6
echo "configure:3478: checking if getrlimit is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3488 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef getrlimit
  error - already defined!
#endif
void getrlimit(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3504: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_GETRLIMIT_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=getsockname;

echo $ac_n "checking if getsockname is prototyped""... $ac_c" 1>&6
echo "configure:3535: checking if getsockname is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3545 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef getsockname
  error - already defined!
#endif
void getsockname(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3561: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_GETSOCKNAME_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=gettimeofday;

echo $ac_n "checking if gettimeofday is prototyped""... $ac_c" 1>&6
echo "configure:3592: checking if gettimeofday is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3602 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef gettimeofday
  error - already defined!
#endif
void gettimeofday(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3618: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_GETTIMEOFDAY_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=htonl;

echo $ac_n "checking if htonl is prototyped""... $ac_c" 1>&6
echo "configure:3649: checking if htonl is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3659 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef htonl
  error - already defined!
#endif
void htonl(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3675: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_HTONL_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=htons;

echo $ac_n "checking if htons is prototyped""... $ac_c" 1>&6
echo "configure:3706: checking if htons is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3716 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef htons
  error - already defined!
#endif
void htons(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3732: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_HTONS_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=inet_addr;

echo $ac_n "checking if inet_addr is prototyped""... $ac_c" 1>&6
echo "configure:3763: checking if inet_addr is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3773 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef inet_addr
  error - already defined!
#endif
void inet_addr(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3789: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_INET_ADDR_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=inet_aton;

echo $ac_n "checking if inet_aton is prototyped""... $ac_c" 1>&6
echo "configure:3820: checking if inet_aton is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3830 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef inet_aton
  error - already defined!
#endif
void inet_aton(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3846: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_INET_ATON_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=inet_ntoa;

echo $ac_n "checking if inet_ntoa is prototyped""... $ac_c" 1>&6
echo "configure:3877: checking if inet_ntoa is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3887 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef inet_ntoa
  error - already defined!
#endif
void inet_ntoa(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3903: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_INET_NTOA_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=listen;

echo $ac_n "checking if listen is prototyped""... $ac_c" 1>&6
echo "configure:3934: checking if listen is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 3944 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef listen
  error - already defined!
#endif
void listen(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:3960: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_LISTEN_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=ntohl;

echo $ac_n "checking if ntohl is prototyped""... $ac_c" 1>&6
echo "configure:3991: checking if ntohl is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4001 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef ntohl
  error - already defined!
#endif
void ntohl(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4017: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_NTOHL_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=perror;

echo $ac_n "checking if perror is prototyped""... $ac_c" 1>&6
echo "configure:4048: checking if perror is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4058 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef perror
  error - already defined!
#endif
void perror(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4074: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_PERROR_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=printf;

echo $ac_n "checking if printf is prototyped""... $ac_c" 1>&6
echo "configure:4105: checking if printf is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4115 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef printf
  error - already defined!
#endif
void printf(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4131: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_PRINTF_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=qsort;

echo $ac_n "checking if qsort is prototyped""... $ac_c" 1>&6
echo "configure:4162: checking if qsort is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4172 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef qsort
  error - already defined!
#endif
void qsort(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4188: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_QSORT_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=read;

echo $ac_n "checking if read is prototyped""... $ac_c" 1>&6
echo "configure:4219: checking if read is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4229 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef read
  error - already defined!
#endif
void read(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4245: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_READ_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=remove;

echo $ac_n "checking if remove is prototyped""... $ac_c" 1>&6
echo "configure:4276: checking if remove is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4286 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef remove
  error - already defined!
#endif
void remove(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4302: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_REMOVE_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=rewind;

echo $ac_n "checking if rewind is prototyped""... $ac_c" 1>&6
echo "configure:4333: checking if rewind is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4343 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef rewind
  error - already defined!
#endif
void rewind(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4359: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_REWIND_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=select;

echo $ac_n "checking if select is prototyped""... $ac_c" 1>&6
echo "configure:4390: checking if select is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4400 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef select
  error - already defined!
#endif
void select(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4416: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SELECT_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=setitimer;

echo $ac_n "checking if setitimer is prototyped""... $ac_c" 1>&6
echo "configure:4447: checking if setitimer is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4457 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef setitimer
  error - already defined!
#endif
void setitimer(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4473: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SETITIMER_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=setrlimit;

echo $ac_n "checking if setrlimit is prototyped""... $ac_c" 1>&6
echo "configure:4504: checking if setrlimit is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4514 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef setrlimit
  error - already defined!
#endif
void setrlimit(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4530: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SETRLIMIT_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=setsockopt;

echo $ac_n "checking if setsockopt is prototyped""... $ac_c" 1>&6
echo "configure:4561: checking if setsockopt is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4571 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef setsockopt
  error - already defined!
#endif
void setsockopt(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4587: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SETSOCKOPT_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=snprintf;

echo $ac_n "checking if snprintf is prototyped""... $ac_c" 1>&6
echo "configure:4618: checking if snprintf is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4628 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef snprintf
  error - already defined!
#endif
void snprintf(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4644: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SNPRINTF_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=socket;

echo $ac_n "checking if socket is prototyped""... $ac_c" 1>&6
echo "configure:4675: checking if socket is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4685 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef socket
  error - already defined!
#endif
void socket(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4701: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SOCKET_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=sprintf;

echo $ac_n "checking if sprintf is prototyped""... $ac_c" 1>&6
echo "configure:4732: checking if sprintf is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4742 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef sprintf
  error - already defined!
#endif
void sprintf(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4758: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SPRINTF_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=sscanf;

echo $ac_n "checking if sscanf is prototyped""... $ac_c" 1>&6
echo "configure:4789: checking if sscanf is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4799 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef sscanf
  error - already defined!
#endif
void sscanf(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4815: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SSCANF_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=strcasecmp;

echo $ac_n "checking if strcasecmp is prototyped""... $ac_c" 1>&6
echo "configure:4846: checking if strcasecmp is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4856 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef strcasecmp
  error - already defined!
#endif
void strcasecmp(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4872: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_STRCASECMP_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=strdup;

echo $ac_n "checking if strdup is prototyped""... $ac_c" 1>&6
echo "configure:4903: checking if strdup is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4913 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef strdup
  error - already defined!
#endif
void strdup(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4929: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_STRDUP_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=strerror;

echo $ac_n "checking if strerror is prototyped""... $ac_c" 1>&6
echo "configure:4960: checking if strerror is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 4970 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef strerror
  error - already defined!
#endif
void strerror(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:4986: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_STRERROR_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=stricmp;

echo $ac_n "checking if stricmp is prototyped""... $ac_c" 1>&6
echo "configure:5017: checking if stricmp is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5027 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef stricmp
  error - already defined!
#endif
void stricmp(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5043: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_STRICMP_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=strlcpy;

echo $ac_n "checking if strlcpy is prototyped""... $ac_c" 1>&6
echo "configure:5074: checking if strlcpy is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5084 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef strlcpy
  error - already defined!
#endif
void strlcpy(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5100: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_STRLCPY_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=strncasecmp;

echo $ac_n "checking if strncasecmp is prototyped""... $ac_c" 1>&6
echo "configure:5131: checking if strncasecmp is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5141 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef strncasecmp
  error - already defined!
#endif
void strncasecmp(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5157: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_STRNCASECMP_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=strnicmp;

echo $ac_n "checking if strnicmp is prototyped""... $ac_c" 1>&6
echo "configure:5188: checking if strnicmp is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5198 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef strnicmp
  error - already defined!
#endif
void strnicmp(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5214: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_STRNICMP_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=system;

echo $ac_n "checking if system is prototyped""... $ac_c" 1>&6
echo "configure:5245: checking if system is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5255 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef system
  error - already defined!
#endif
void system(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5271: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_SYSTEM_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=time;

echo $ac_n "checking if time is prototyped""... $ac_c" 1>&6
echo "configure:5302: checking if time is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5312 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef time
  error - already defined!
#endif
void time(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5328: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_TIME_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=unlink;

echo $ac_n "checking if unlink is prototyped""... $ac_c" 1>&6
echo "configure:5359: checking if unlink is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5369 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef unlink
  error - already defined!
#endif
void unlink(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5385: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_UNLINK_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=vsnprintf;

echo $ac_n "checking if vsnprintf is prototyped""... $ac_c" 1>&6
echo "configure:5416: checking if vsnprintf is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5426 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef vsnprintf
  error - already defined!
#endif
void vsnprintf(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5442: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_VSNPRINTF_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


ac_safe=write;

echo $ac_n "checking if write is prototyped""... $ac_c" 1>&6
echo "configure:5473: checking if write is prototyped" >&5
if eval "test \"`echo '$''{'ac_cv_prototype_$ac_safe'+set}'`\" = set"; then
  echo $ac_n "(cached) $ac_c" 1>&6
else
  #
  if test $ac_cv_gcc_fnb = yes; then
    OLDCFLAGS=$CFLAGS
    CFLAGS="$CFLAGS -fno-builtin"
  fi
cat > conftest.$ac_ext <<EOF
#line 5483 "configure"
#include "confdefs.h"

#define NO_LIBRARY_PROTOTYPES
#define __COMM_C__
#define __ACT_OTHER_C__
#include "src/sysdep.h"
#ifdef write
  error - already defined!
#endif
void write(int a, char b, int c, char d, int e, char f, int g, char h);

int main() {

; return 0; }
EOF
if { (eval echo configure:5499: \"$ac_compile\") 1>&5; (eval $ac_compile) 2>&5; }; then
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=no"
else
  echo "configure: failed program was:" >&5
  cat conftest.$ac_ext >&5
  rm -rf conftest*
  eval "ac_cv_prototype_$ac_safe=yes"
fi
rm -f conftest*
  if test $ac_cv_gcc_fnb = yes; then
    CFLAGS=$OLDCFLAGS
  fi

fi


if eval "test \"`echo '$ac_cv_prototype_'$ac_safe`\" = yes"; then
  echo "$ac_t""yes" 1>&6
else
  cat >> confdefs.h <<\EOF
#define NEED_WRITE_PROTO 
EOF

  echo "$ac_t""no" 1>&6
fi


trap '' 1 2 15
cat > confcache <<\EOF
# This file is a shell script that caches the results of configure
# tests run on this system so they can be shared between configure
# scripts and configure runs.  It is not useful on other systems.
# If it contains results you don't want to keep, you may remove or edit it.
#
# By default, configure uses ./config.cache as the cache file,
# creating it if it does not exist already.  You can give configure
# the --cache-file=FILE option to use a different cache file; that is
# what configure does when it calls configure scripts in
# subdirectories, so they share the cache.
# Giving --cache-file=/dev/null disables caching, for debugging configure.
# config.status only pays attention to the cache file if you give it the
# --recheck option to rerun configure.
#
EOF
# The following way of writing the cache mishandles newlines in values,
# but we know of no workaround that is simple, portable, and efficient.
# So, don't put newlines in cache variables' values.
# Ultrix sh set writes to stderr and can't be redirected directly,
# and sets the high bit in the cache file unless we assign to the vars.
(set) 2>&1 |
  case `(ac_space=' '; set | grep ac_space) 2>&1` in
  *ac_space=\ *)
    # `set' does not quote correctly, so add quotes (double-quote substitution
    # turns \\\\ into \\, and sed turns \\ into \).
    sed -n \
      -e "s/'/'\\\\''/g" \
      -e "s/^\\([a-zA-Z0-9_]*_cv_[a-zA-Z0-9_]*\\)=\\(.*\\)/\\1=\${\\1='\\2'}/p"
    ;;
  *)
    # `set' quotes correctly as required by POSIX, so do not add quotes.
    sed -n -e 's/^\([a-zA-Z0-9_]*_cv_[a-zA-Z0-9_]*\)=\(.*\)/\1=${\1=\2}/p'
    ;;
  esac >> confcache
if cmp -s $cache_file confcache; then
  :
else
  if test -w $cache_file; then
    echo "updating cache $cache_file"
    cat confcache > $cache_file
  else
    echo "not updating unwritable cache $cache_file"
  fi
fi
rm -f confcache

trap 'rm -fr conftest* confdefs* core core.* *.core $ac_clean_files; exit 1' 1 2 15

test "x$prefix" = xNONE && prefix=$ac_default_prefix
# Let make expand exec_prefix.
test "x$exec_prefix" = xNONE && exec_prefix='${prefix}'

# Any assignment to VPATH causes Sun make to only execute
# the first set of double-colon rules, so remove it if not needed.
# If there is a colon in the path, we need to keep it.
if test "x$srcdir" = x.; then
  ac_vpsub='/^[ 	]*VPATH[ 	]*=[^:]*$/d'
fi

trap 'rm -f $CONFIG_STATUS conftest*; exit 1' 1 2 15

DEFS=-DHAVE_CONFIG_H

# Without the "./", some shells look in PATH for config.status.
: ${CONFIG_STATUS=./config.status}

echo creating $CONFIG_STATUS
rm -f $CONFIG_STATUS
cat > $CONFIG_STATUS <<EOF
#! /bin/sh
# Generated automatically by configure.
# Run this file to recreate the current configuration.
# This directory was configured as follows,
# on host `(hostname || uname -n) 2>/dev/null | sed 1q`:
#
# $0 $ac_configure_args
#
# Compiler output produced by configure, useful for debugging
# configure, is in ./config.log if it exists.

ac_cs_usage="Usage: $CONFIG_STATUS [--recheck] [--version] [--help]"
for ac_option
do
  case "\$ac_option" in
  -recheck | --recheck | --rechec | --reche | --rech | --rec | --re | --r)
    echo "running \${CONFIG_SHELL-/bin/sh} $0 $ac_configure_args --no-create --no-recursion"
    exec \${CONFIG_SHELL-/bin/sh} $0 $ac_configure_args --no-create --no-recursion ;;
  -version | --version | --versio | --versi | --vers | --ver | --ve | --v)
    echo "$CONFIG_STATUS generated by autoconf version 2.13"
    exit 0 ;;
  -help | --help | --hel | --he | --h)
    echo "\$ac_cs_usage"; exit 0 ;;
  *) echo "\$ac_cs_usage"; exit 1 ;;
  esac
done

ac_given_srcdir=$srcdir

trap 'rm -fr `echo "src/Makefile src/util/Makefile src/conf.h" | sed "s/:[^ ]*//g"` conftest*; exit 1' 1 2 15
EOF
cat >> $CONFIG_STATUS <<EOF

# Protect against being on the right side of a sed subst in config.status.
sed 's/%@/@@/; s/@%/@@/; s/%g\$/@g/; /@g\$/s/[\\\\&%]/\\\\&/g;
 s/@@/%@/; s/@@/@%/; s/@g\$/%g/' > conftest.subs <<\\CEOF
$ac_vpsub
$extrasub
s%@SHELL@%$SHELL%g
s%@CFLAGS@%$CFLAGS%g
s%@CPPFLAGS@%$CPPFLAGS%g
s%@CXXFLAGS@%$CXXFLAGS%g
s%@FFLAGS@%$FFLAGS%g
s%@DEFS@%$DEFS%g
s%@LDFLAGS@%$LDFLAGS%g
s%@LIBS@%$LIBS%g
s%@exec_prefix@%$exec_prefix%g
s%@prefix@%$prefix%g
s%@program_transform_name@%$program_transform_name%g
s%@bindir@%$bindir%g
s%@sbindir@%$sbindir%g
s%@libexecdir@%$libexecdir%g
s%@datadir@%$datadir%g
s%@sysconfdir@%$sysconfdir%g
s%@sharedstatedir@%$sharedstatedir%g
s%@localstatedir@%$localstatedir%g
s%@libdir@%$libdir%g
s%@includedir@%$includedir%g
s%@oldincludedir@%$oldincludedir%g
s%@infodir@%$infodir%g
s%@mandir@%$mandir%g
s%@MYFLAGS@%$MYFLAGS%g
s%@NETLIB@%$NETLIB%g
s%@CRYPTLIB@%$CRYPTLIB%g
s%@MORE@%$MORE%g
s%@CC@%$CC%g
s%@CPP@%$CPP%g

CEOF
EOF

cat >> $CONFIG_STATUS <<\EOF

# Split the substitutions into bite-sized pieces for seds with
# small command number limits, like on Digital OSF/1 and HP-UX.
ac_max_sed_cmds=90 # Maximum number of lines to put in a sed script.
ac_file=1 # Number of current file.
ac_beg=1 # First line for current file.
ac_end=$ac_max_sed_cmds # Line after last line for current file.
ac_more_lines=:
ac_sed_cmds=""
while $ac_more_lines; do
  if test $ac_beg -gt 1; then
    sed "1,${ac_beg}d; ${ac_end}q" conftest.subs > conftest.s$ac_file
  else
    sed "${ac_end}q" conftest.subs > conftest.s$ac_file
  fi
  if test ! -s conftest.s$ac_file; then
    ac_more_lines=false
    rm -f conftest.s$ac_file
  else
    if test -z "$ac_sed_cmds"; then
      ac_sed_cmds="sed -f conftest.s$ac_file"
    else
      ac_sed_cmds="$ac_sed_cmds | sed -f conftest.s$ac_file"
    fi
    ac_file=`expr $ac_file + 1`
    ac_beg=$ac_end
    ac_end=`expr $ac_end + $ac_max_sed_cmds`
  fi
done
if test -z "$ac_sed_cmds"; then
  ac_sed_cmds=cat
fi
EOF

cat >> $CONFIG_STATUS <<EOF

CONFIG_FILES=\${CONFIG_FILES-"Makefile util/Makefile"}
EOF
cat >> $CONFIG_STATUS <<\EOF
for ac_file in .. $CONFIG_FILES; do if test "x$ac_file" != x..; then
  # Support "outfile[:infile[:infile...]]", defaulting infile="outfile.in".
  case "$ac_file" in
  *:*) ac_file_in=`echo "$ac_file"|sed 's%[^:]*:%%'`
       ac_file=`echo "$ac_file"|sed 's%:.*%%'` ;;
  *) ac_file_in="${ac_file}.in" ;;
  esac

  # Adjust a relative srcdir, top_srcdir, and INSTALL for subdirectories.

  # Remove last slash and all that follows it.  Not all systems have dirname.
  ac_dir=`echo $ac_file|sed 's%/[^/][^/]*$%%'`
  if test "$ac_dir" != "$ac_file" && test "$ac_dir" != .; then
    # The file is in a subdirectory.
    test ! -d "$ac_dir" && mkdir "$ac_dir"
    ac_dir_suffix="/`echo $ac_dir|sed 's%^\./%%'`"
    # A "../" for each directory in $ac_dir_suffix.
    ac_dots=`echo $ac_dir_suffix|sed 's%/[^/]*%../%g'`
  else
    ac_dir_suffix= ac_dots=
  fi

  case "$ac_given_srcdir" in
  .)  srcdir=.
      if test -z "$ac_dots"; then top_srcdir=.
      else top_srcdir=`echo $ac_dots|sed 's%/$%%'`; fi ;;
  /*) srcdir="$ac_given_srcdir$ac_dir_suffix"; top_srcdir="$ac_given_srcdir" ;;
  *) # Relative path.
    srcdir="$ac_dots$ac_given_srcdir$ac_dir_suffix"
    top_srcdir="$ac_dots$ac_given_srcdir" ;;
  esac


  echo creating "$ac_file"
  rm -f "$ac_file"
  configure_input="Generated automatically from `echo $ac_file_in|sed 's%.*/%%'` by configure."
  case "$ac_file" in
  *Makefile*) ac_comsub="1i\\
# $configure_input" ;;
  *) ac_comsub= ;;
  esac

  ac_file_inputs=`echo $ac_file_in|sed -e "s%^%$ac_given_srcdir/%" -e "s%:% $ac_given_srcdir/%g"`
  sed -e "$ac_comsub
s%@configure_input@%$configure_input%g
s%@srcdir@%$srcdir%g
s%@top_srcdir@%$top_srcdir%g
" $ac_file_inputs | (eval "$ac_sed_cmds") > $ac_file
fi; done
rm -f conftest.s*

# These sed commands are passed to sed as "A NAME B NAME C VALUE D", where
# NAME is the cpp macro being defined and VALUE is the value it is being given.
#
# ac_d sets the value in "#define NAME VALUE" lines.
ac_dA='s%^\([ 	]*\)#\([ 	]*define[ 	][ 	]*\)'
ac_dB='\([ 	][ 	]*\)[^ 	]*%\1#\2'
ac_dC='\3'
ac_dD='%g'
# ac_u turns "#undef NAME" with trailing blanks into "#define NAME VALUE".
ac_uA='s%^\([ 	]*\)#\([ 	]*\)undef\([ 	][ 	]*\)'
ac_uB='\([ 	]\)%\1#\2define\3'
ac_uC=' '
ac_uD='\4%g'
# ac_e turns "#undef NAME" without trailing blanks into "#define NAME VALUE".
ac_eA='s%^\([ 	]*\)#\([ 	]*\)undef\([ 	][ 	]*\)'
ac_eB='$%\1#\2define\3'
ac_eC=' '
ac_eD='%g'

if test "${CONFIG_HEADERS+set}" != set; then
EOF
cat >> $CONFIG_STATUS <<EOF
  CONFIG_HEADERS="src/conf.h"
EOF
cat >> $CONFIG_STATUS <<\EOF
fi
for ac_file in .. $CONFIG_HEADERS; do if test "x$ac_file" != x..; then
  # Support "outfile[:infile[:infile...]]", defaulting infile="outfile.in".
  case "$ac_file" in
  *:*) ac_file_in=`echo "$ac_file"|sed 's%[^:]*:%%'`
       ac_file=`echo "$ac_file"|sed 's%:.*%%'` ;;
  *) ac_file_in="${ac_file}.in" ;;
  esac

  echo creating $ac_file

  rm -f conftest.frag conftest.in conftest.out
  ac_file_inputs=`echo $ac_file_in|sed -e "s%^%$ac_given_srcdir/%" -e "s%:% $ac_given_srcdir/%g"`
  cat $ac_file_inputs > conftest.in

EOF

# Transform confdefs.h into a sed script conftest.vals that substitutes
# the proper values into config.h.in to produce config.h.  And first:
# Protect against being on the right side of a sed subst in config.status.
# Protect against being in an unquoted here document in config.status.
rm -f conftest.vals
cat > conftest.hdr <<\EOF
s/[\\&%]/\\&/g
s%[\\$`]%\\&%g
s%#define \([A-Za-z_][A-Za-z0-9_]*\) *\(.*\)%${ac_dA}\1${ac_dB}\1${ac_dC}\2${ac_dD}%gp
s%ac_d%ac_u%gp
s%ac_u%ac_e%gp
EOF
sed -n -f conftest.hdr confdefs.h > conftest.vals
rm -f conftest.hdr

# This sed command replaces #undef with comments.  This is necessary, for
# example, in the case of _POSIX_SOURCE, which is predefined and required
# on some systems where configure will not decide to define it.
cat >> conftest.vals <<\EOF
s%^[ 	]*#[ 	]*undef[ 	][ 	]*[a-zA-Z_][a-zA-Z_0-9]*%/* & */%
EOF

# Break up conftest.vals because some shells have a limit on
# the size of here documents, and old seds have small limits too.

rm -f conftest.tail
while :
do
  ac_lines=`grep -c . conftest.vals`
  # grep -c gives empty output for an empty file on some AIX systems.
  if test -z "$ac_lines" || test "$ac_lines" -eq 0; then break; fi
  # Write a limited-size here document to conftest.frag.
  echo '  cat > conftest.frag <<CEOF' >> $CONFIG_STATUS
  sed ${ac_max_here_lines}q conftest.vals >> $CONFIG_STATUS
  echo 'CEOF
  sed -f conftest.frag conftest.in > conftest.out
  rm -f conftest.in
  mv conftest.out conftest.in
' >> $CONFIG_STATUS
  sed 1,${ac_max_here_lines}d conftest.vals > conftest.tail
  rm -f conftest.vals
  mv conftest.tail conftest.vals
done
rm -f conftest.vals

cat >> $CONFIG_STATUS <<\EOF
  rm -f conftest.frag conftest.h
  echo "/* $ac_file.  Generated automatically by configure.  */" > conftest.h
  cat conftest.in >> conftest.h
  rm -f conftest.in
  if cmp -s $ac_file conftest.h 2>/dev/null; then
    echo "$ac_file is unchanged"
    rm -f conftest.h
  else
    # Remove last slash and all that follows it.  Not all systems have dirname.
      ac_dir=`echo $ac_file|sed 's%/[^/][^/]*$%%'`
      if test "$ac_dir" != "$ac_file" && test "$ac_dir" != .; then
      # The file is in a subdirectory.
      test ! -d "$ac_dir" && mkdir "$ac_dir"
    fi
    rm -f $ac_file
    mv conftest.h $ac_file
  fi
fi; done

EOF
cat >> $CONFIG_STATUS <<EOF

EOF
cat >> $CONFIG_STATUS <<\EOF

exit 0
EOF
chmod +x $CONFIG_STATUS
rm -fr confdefs* $ac_clean_files
test "$no_create" = yes || ${CONFIG_SHELL-/bin/sh} $CONFIG_STATUS || exit 1

#
echo "Configuration completed.  To compile, type:  make"
