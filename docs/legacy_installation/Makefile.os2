# CircleMUD Makefile for OS/2 (manually created by <PERSON>)

# C compiler to use
CC = gcc

# Any special flags you want to pass to the compiler
MYFLAGS = -O2 -Wall

#flags for profiling (see hacker.doc for more information)
PROFILE =

# Libraires that need to be included for use with GCC for OS/2
LIB = -lsocket 

##############################################################################
# Do Not Modify Anything Below This Line (unless you know what you're doing) #
##############################################################################

# For compiling circle with GDB debugger Information
#CFLAGS = -g -O $(MYFLAGS) $(PROFILE)
# Uncomment the line below if you don't want to compile with GDB info
CFLAGS = $(MYFLAGS) $(PROFILE) 

OBJFILES = comm.o act.comm.o act.informative.o act.movement.o act.item.o \
        act.offensive.o act.other.o act.social.o act.wizard.o ban.o boards.o \
        castle.o class.o config.o constants.o db.o fight.o graph.o handler.o \
        house.o interpreter.o limits.o magic.o mail.o mobact.o modify.o \
        objsave.o shop.o spec_assign.o spec_procs.o spell_parser.o \
        spells.o utils.o weather.o random.o players.o quest.o qedit.o genqst.o

default: .accepted
        $(MAKE) ../bin/circle

.accepted:
        @./licheck more

utils: .accepted
        $(MAKE) ../bin/asciipasswd
	$(MAKE) ../bin/autowiz
        $(MAKE) ../bin/listrent
        $(MAKE) ../bin/plrtoascii
	$(MAKE) ../bin/shopconv
        $(MAKE) ../bin/sign
        $(MAKE) ../bin/split
        $(MAKE) ../bin/wld2html

all: .accepted
        $(MAKE) ../bin/circle
        $(MAKE) utils

circle:
        $(MAKE) ../bin/circle
asciipasswd:
	$(MAKE) ../bin/asciipasswd
autowiz:
        $(MAKE) ../bin/autowiz
listrent:
        $(MAKE) ../bin/listrent
plrtoascii:
	$(MAKE) ../bin/plrtoascii
shopconv:
        $(MAKE) ../bin/shopconv
sign:
        $(MAKE) ../bin/sign
split:
        $(MAKE) ../bin/split
wld2html:
        $(MAKE) ../bin/wld2html


../bin/asciipasswd: util/asciipasswd.c conf.h sysdep.h  structs.h utils.h
	$(CC) $(CFLAGS) -o ../bin/asciipasswd util/asciipasswd.c
../bin/autowiz: util/autowiz.c conf.h sysdep.h structs.h utils.h db.h
        $(CC) $(CFLAGS) -o ../bin/autowiz util/autowiz.c
../bin/listrent: util/listrent.c conf.h sysdep.h structs.h
        $(CC) $(CFLAGS) -o ../bin/listrent util/listrent.c
../bin/plrtoascii: util/plrtoascii.c conf.h sysdep.h db.h pfdefaults.h
	$(CC) $(CFLAGS) -o ../bin/plrtoascii util/plrtoascii.c
../bin/shopconv: util/shopconv.c conf.h sysdep.h structs.h db.h utils.h shop.h
        $(CC) $(CFLAGS) -o ../bin/shopconv util/shopconv.c
../bin/sign: util/sign.c conf.h sysdep.h
        $(CC) $(CFLAGS) -o ../bin/sign util/sign.c 
../bin/split: util/split.c
        $(CC) $(CFLAGS) -o ../bin/split util/split.c
../bin/wld2html: util/wld2html.c
        $(CC) $(CFLAGS) -o ../bin/wld2html util/wld2html.c

../bin/circle : $(OBJFILES)
        $(CC) -o circle.exe $(PROFILE) $(OBJFILES) $(LIB) 
clean:
        rm -f *.o

# Dependencies for the object files (automagically generated with
# gcc -MM)

act.comm.o: act.comm.c conf.h sysdep.h structs.h utils.h comm.h interpreter.h \
  handler.h db.h screen.h
        $(CC) -c $(CFLAGS) act.comm.c
act.informative.o: act.informative.c conf.h sysdep.h structs.h utils.h comm.h \
  interpreter.h handler.h db.h spells.h screen.h constants.h
        $(CC) -c $(CFLAGS) act.informative.c
act.item.o: act.item.c conf.h sysdep.h structs.h utils.h comm.h interpreter.h \
  handler.h db.h spells.h
        $(CC) -c $(CFLAGS) act.item.c
act.movement.o: act.movement.c conf.h sysdep.h structs.h utils.h comm.h \
  interpreter.h handler.h db.h spells.h house.h constants.h
        $(CC) -c $(CFLAGS) act.movement.c
act.offensive.o: act.offensive.c conf.h sysdep.h structs.h utils.h comm.h \
  interpreter.h handler.h db.h spells.h
        $(CC) -c $(CFLAGS) act.offensive.c
act.other.o: act.other.c conf.h sysdep.h structs.h utils.h comm.h interpreter.h \
  handler.h db.h spells.h screen.h house.h
        $(CC) -c $(CFLAGS) act.other.c
act.social.o: act.social.c conf.h sysdep.h structs.h utils.h comm.h \
  interpreter.h handler.h db.h spells.h
        $(CC) -c $(CFLAGS) act.social.c
act.wizard.o: act.wizard.c conf.h sysdep.h structs.h utils.h comm.h \
  interpreter.h handler.h db.h spells.h house.h screen.h constants.h
        $(CC) -c $(CFLAGS) act.wizard.c
ban.o: ban.c conf.h sysdep.h structs.h utils.h comm.h interpreter.h handler.h db.h
        $(CC) -c $(CFLAGS) ban.c
boards.o: boards.c conf.h sysdep.h structs.h utils.h comm.h db.h boards.h \
  interpreter.h handler.h
        $(CC) -c $(CFLAGS) boards.c
castle.o: castle.c conf.h sysdep.h structs.h utils.h comm.h interpreter.h \
  handler.h db.h spells.h
        $(CC) -c $(CFLAGS) castle.c
class.o: class.c conf.h sysdep.h structs.h db.h utils.h spells.h interpreter.h
        $(CC) -c $(CFLAGS) class.c
comm.o: comm.c conf.h sysdep.h structs.h utils.h comm.h interpreter.h handler.h \
  db.h house.h
        $(CC) -c $(CFLAGS) comm.c
config.o: config.c conf.h sysdep.h structs.h
        $(CC) -c $(CFLAGS) config.c
constants.o: constants.c conf.h sysdep.h structs.h
        $(CC) -c $(CFLAGS) constants.c
db.o: db.c conf.h sysdep.h structs.h utils.h db.h comm.h handler.h spells.h mail.h \
  interpreter.h house.h
        $(CC) -c $(CFLAGS) db.c
fight.o: fight.c conf.h sysdep.h structs.h utils.h comm.h handler.h interpreter.h \
  db.h spells.h screen.h
        $(CC) -c $(CFLAGS) fight.c
graph.o: graph.c conf.h sysdep.h structs.h utils.h comm.h interpreter.h handler.h \
  db.h spells.h
        $(CC) -c $(CFLAGS) graph.c
handler.o: handler.c conf.h sysdep.h structs.h utils.h comm.h db.h handler.h \
  interpreter.h spells.h
        $(CC) -c $(CFLAGS) handler.c
house.o: house.c conf.h sysdep.h structs.h comm.h handler.h db.h interpreter.h \
  utils.h house.h constants.h
        $(CC) -c $(CFLAGS) house.c
interpreter.o: interpreter.c conf.h sysdep.h structs.h comm.h interpreter.h db.h \
  utils.h spells.h handler.h mail.h screen.h
        $(CC) -c $(CFLAGS) interpreter.c
limits.o: limits.c conf.h sysdep.h structs.h utils.h spells.h comm.h db.h \
  handler.h
        $(CC) -c $(CFLAGS) limits.c
magic.o: magic.c conf.h sysdep.h structs.h utils.h comm.h spells.h handler.h db.h
        $(CC) -c $(CFLAGS) magic.c
mail.o: mail.c conf.h sysdep.h structs.h utils.h comm.h db.h interpreter.h \
  handler.h mail.h
        $(CC) -c $(CFLAGS) mail.c
mobact.o: mobact.c conf.h sysdep.h structs.h utils.h db.h comm.h interpreter.h \
  handler.h spells.h
        $(CC) -c $(CFLAGS) mobact.c
modify.o: modify.c conf.h sysdep.h structs.h utils.h interpreter.h handler.h db.h \
  comm.h spells.h mail.h boards.h
        $(CC) -c $(CFLAGS) modify.c
objsave.o: objsave.c conf.h sysdep.h structs.h comm.h handler.h db.h \
  interpreter.h utils.h spells.h
        $(CC) -c $(CFLAGS) objsave.c
players.o: players.c conf.h sysdep.h structs.h utils.h db.h handler.h pfdefaults.h
        $(CC) -c $(CFLAGS) players.c
random.o: random.c
        $(CC) -c $(CFLAGS) random.c
shop.o: shop.c conf.h sysdep.h structs.h comm.h handler.h db.h interpreter.h \
  utils.h shop.h
        $(CC) -c $(CFLAGS) shop.c
spec_assign.o: spec_assign.c conf.h sysdep.h structs.h db.h interpreter.h \
  utils.h
        $(CC) -c $(CFLAGS) spec_assign.c
spec_procs.o: spec_procs.c conf.h sysdep.h structs.h utils.h comm.h \
  interpreter.h handler.h db.h spells.h
        $(CC) -c $(CFLAGS) spec_procs.c
spell_parser.o: spell_parser.c conf.h sysdep.h structs.h utils.h interpreter.h \
  spells.h handler.h comm.h db.h
        $(CC) -c $(CFLAGS) spell_parser.c
spells.o: spells.c conf.h sysdep.h structs.h utils.h comm.h spells.h handler.h \
  db.h constants.h
        $(CC) -c $(CFLAGS) spells.c
utils.o: utils.c conf.h sysdep.h structs.h utils.h comm.h screen.h spells.h \
  handler.h
        $(CC) -c $(CFLAGS) utils.c
weather.o: weather.c conf.h sysdep.h structs.h utils.h comm.h handler.h \
  interpreter.h db.h
        $(CC) -c $(CFLAGS) weather.c
quest.obj: quest.c conf.h sysdep.h structs.h utils.h interpreter.h handler.h \
  comm.h db.h screen.h quest.h
        $(CC) -c $(CFLAGS) quest.c
qedit.obj: qedit.c conf.h sysdep.h structs.h utils.h comm.h db.h oasis.h \
  improved-edit.h screen.h genolc.h genzon.h interpreter.h quest.h
        $(CC) -c $(CFLAGS) qedit.c
genqst.obj: genqst.c conf.h sysdep.h structs.h utils.h db.h quest.h \
  genolc.h genzon.h 
        $(CC) -c $(CFLAGS) genqst.c
