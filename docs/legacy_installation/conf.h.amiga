/* CircleMUD for Amiga conf.h file - manually created (<PERSON><PERSON> 26 Jul 1996)

/* src/conf.h.in.  Generated automatically from configure.in by autoheader.  */

#ifndef _CONF_H_
#define _CONF_H_

#undef CIRCLE_WINDOWS

#define AMIGA 1

/* Define to empty if the keyword does not work.  */
/* #undef const */

/* Define if you have <sys/wait.h> that is POSIX.1 compatible.  */
/* #undef HAVE_SYS_WAIT_H */

/* Define to `int' if <sys/types.h> doesn't define.  */
/*#define pid_t int*/

/* Define as the return type of signal handlers (int or void).  */
#define RETSIGTYPE void

/* Define to `unsigned' if <sys/types.h> doesn't define.  */
/* #undef size_t */

/* Define if you have 'struct in_addr' */
#define HAVE_STRUCT_IN_ADDR 1

/* Define if your crypt isn't safe with only 10 characters. */
#undef HAVE_UNSAFE_CRYPT

/* Define to `int' if <sys/socket.h> doesn't define.  */
#define socklen_t int

/* Define if you have the ANSI C header files.  */
#define STDC_HEADERS 1

/* Define if you can safely include both <sys/time.h> and <time.h>.  */
#define TIME_WITH_SYS_TIME 1

/* Define if you have the crypt function.  */
#undef CIRCLE_CRYPT

/* Define if you have the random function.  */
#undef HAVE_RANDOM

/* Define if you have the <arpa/telnet.h> header file.  */
#define HAVE_ARPA_TELNET_H 1

/* Define if you have the <assert.h> header file.  */
#define HAVE_ASSERT_H 1

/* Define if you have the <crypt.h> header file.  */
#undef HAVE_CRYPT_H

/* Define if you have the <errno.h> header file.  */
#define HAVE_ERRNO_H 1

/* Define if you have the <fcntl.h> header file.  */
#define HAVE_FCNTL_H 1

/* Define if you have the <limits.h> header file.  */
#define HAVE_LIMITS_H 1

/* Define if you have the <memory.h> header file.  */
#define HAVE_MEMORY_H 1

/* Define if you have the <net/errno.h> header file.  */
/* #undef HAVE_NET_ERRNO_H */

/* Define if you have the <string.h> header file.  */
#define HAVE_STRING_H 1

/* Define if you have the <sys/fcntl.h> header file.  */
#define HAVE_SYS_FCNTL_H 1

/* Define if you have the <sys/select.h> header file.  */
/* #undef HAVE_SYS_SELECT_H */

/* Define if you have the <sys/time.h> header file.  */
#define HAVE_SYS_TIME_H 1

/* Define if you have the <sys/types.h> header file.  */
#define HAVE_SYS_TYPES_H 1

/* Define if you have the <unistd.h> header file.  */
#define HAVE_UNISTD_H 1

/* Define if you have the crypt library (-lcrypt).  */
/* #undef HAVE_LIBCRYPT */

/* Define if you have the malloc library (-lmalloc).  */
/* #undef HAVE_LIBMALLOC */

/* Define if you have the nsl library (-lnsl).  */
/* #undef HAVE_LIBNSL */

/* Define if you have the socket library (-lsocket).  */
/* #undef HAVE_LIBSOCKET */

/* Define if your compiler does not prototype remove().  */
/* #undef NEED_REMOVE_PROTO */

/* Define if your compiler does not prototype strerror().  */
/* #undef NEED_STRERROR_PROTO */

#endif /* _CONF_H_ */
