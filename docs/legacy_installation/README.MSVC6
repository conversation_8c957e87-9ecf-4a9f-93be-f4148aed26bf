                 Compiling CircleMUD under Microsoft Windows 95
                     using Microsoft Visual C++ 6.0 w/SP2


The following information is from <PERSON>.  You can contact <PERSON> at
<<EMAIL>>. [1]

Compiling with MS Visual C++ 6.0:

1. In the src directory, rename conf.h.win to conf.h.

2. Go to File|New... Create a new workspace called circle. Put the root
   directory path into the location field.

3. Go to File|New... Create a new Win32 Console Application called circle
   and click to add it to the current workspace.  Chose an empty console.

4. In fileview, right-click circle files, and click Add Files to Project...
   Select all of the .c files in the src directory.

5. Expand the plus sign of Circle files, right-click the headers Folder,
   and click Add Files to Folder... Select all of the .h files in the src
   directory. 

5. Right-click "Circle files", click settings..., and change settings to
   all configurations.

6. Click the Link tab and add wsock32.lib to end of the Object/Library
   modules list.
  
7. Save the workspace. 

8. Choose Build|Build circle.exe.

9. Move the circle.exe file from the circle\debug directory to the root
   directory. 

=========

[1] - This appears (by 'diff') to be based on the README.MSVC5 document by <PERSON> from http://www.connect.ab.ca/~rbmstrk/.  You can contact Rob at his
<<EMAIL>> e-mail address.

George Greer
